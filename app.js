class OastePresentationApp {
    constructor() {
        this.currentSlide = 0;
        this.totalSlides = 24;
        this.selectedAge = null;
        this.slides = [];
        this.isPlaying = false;
        this.achievements = new Map();
        this.sections = [
            { name: "Identitatea Noastră", start: 0, end: 7 },
            { name: "Viața Spirituală", start: 8, end: 15 },
            { name: "Aplicarea Practică", start: 16, end: 23 }
        ];
        this.currentSection = 0;
        this.visitedSlides = new Set();
        this.interactionCount = 0;
        this.startTime = Date.now();

        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.createAllSlides();
        this.initializeAchievements();
        this.setupAccessibility();
        this.showAgeSelector();
    }
    
    setupEventListeners() {
        // Age selection
        document.querySelectorAll('.age-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectAge(e.target.dataset.age);
            });
        });
        
        // Navigation
        document.getElementById('prevBtn').addEventListener('click', () => this.previousSlide());
        document.getElementById('nextBtn').addEventListener('click', () => this.nextSlide());
        
        // Sidebar
        document.getElementById('menuToggle').addEventListener('click', () => this.toggleSidebar());
        document.getElementById('sidebarClose').addEventListener('click', () => this.closeSidebar());
        
        // Sidebar navigation
        document.querySelectorAll('.sidebar a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const slideIndex = parseInt(e.target.dataset.slide);
                this.goToSlide(slideIndex);
                this.closeSidebar();
            });
        });
        
        // Interactive buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('interactive-btn')) {
                this.handleInteractiveAction(e.target.dataset.action);
            }
        });
        
        // Modal
        document.getElementById('modalClose').addEventListener('click', () => this.closeModal());
        document.getElementById('interactiveModal').addEventListener('click', (e) => {
            if (e.target.id === 'interactiveModal') {
                this.closeModal();
            }
        });
        
        // Restart presentation
        document.getElementById('restartPresentation').addEventListener('click', () => {
            this.restartPresentation();
        });
        
        // Enhanced keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                e.preventDefault();
                this.previousSlide();
            }
            if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                e.preventDefault();
                this.nextSlide();
            }
            if (e.key === 'Escape') {
                e.preventDefault();
                this.closeModal();
                this.closeAchievements();
            }
            if (e.key === 'Home') {
                e.preventDefault();
                this.goToSlide(0);
            }
            if (e.key === 'End') {
                e.preventDefault();
                this.goToSlide(this.totalSlides - 1);
            }
        });

        // Achievement system events
        document.getElementById('achievementsToggle').addEventListener('click', () => {
            this.toggleAchievements();
        });

        // Touch/swipe support for mobile
        this.setupTouchEvents();
    }
    
    createAllSlides() {
        const slidesContainer = document.getElementById('slidesContainer');
        
        // Define all slide content
        const slideContents = [
            // Slides 0-4 already exist in HTML, so we'll add the missing ones
            
            // Slide 5: Simbolurile noastre
            {
                title: "Simbolurile noastre sfinte",
                content: `
                    <div class="symbols-grid">
                        <div class="symbol-item">
                            <div class="symbol">✠</div>
                            <h3>Crucea Ortodoxă</h3>
                            <p class="age-content" data-age="8-10">Semnul dragostei lui Iisus pentru noi!</p>
                            <p class="age-content" data-age="11-14">Simbolul jertfei și învierii Mântuitorului nostru.</p>
                            <p class="age-content" data-age="15-18">Expresia centrală a credinței ortodoxe și a mărturisirii jertfei lui Hristos.</p>
                        </div>
                        <div class="symbol-item">
                            <div class="symbol">🏛️</div>
                            <h3>Biserica</h3>
                            <p class="age-content" data-age="8-10">Casa noastră duhovnicească!</p>
                            <p class="age-content" data-age="11-14">Locul unde ne întâlnim cu Dumnezeu și cu credincioșii.</p>
                            <p class="age-content" data-age="15-18">Comunitatea euharistică și trupul mistic al lui Hristos.</p>
                        </div>
                    </div>
                    <button class="btn btn--primary interactive-btn" data-action="symbolsQuiz">🎯 Quiz despre simboluri</button>
                `
            },
            
            // Slide 6: Biserica noastră
            {
                title: "Biserica noastră - Casa lui Dumnezeu",
                content: `
                    <div class="church-info">
                        <div class="church-visual">⛪</div>
                        <div class="church-description">
                            <p class="age-content" data-age="8-10">Biserica este casa lui Dumnezeu unde mergem să ne rugăm și să cântăm!</p>
                            <p class="age-content" data-age="11-14">În biserica ortodoxă ne întâlnim cu Dumnezeu prin rugăciune, cântare și Sfintele Taine.</p>
                            <p class="age-content" data-age="15-18">Biserica Ortodoxă Română este păstrătoarea tradiției apostolice și a adevăratei credinți creștine.</p>
                        </div>
                    </div>
                    <div class="church-elements">
                        <div class="element">🕯️ Lumânări</div>
                        <div class="element">📖 Evanghelia</div>
                        <div class="element">🍞 Euharistia</div>
                    </div>
                    <button class="btn btn--secondary interactive-btn" data-action="churchTour">🏛️ Turul bisericii</button>
                `
            }
        ];
        
        // Add the missing slides to the container
        slideContents.forEach((slide, index) => {
            const slideIndex = index + 5; // Starting from slide 5
            if (slideIndex < this.totalSlides && !document.querySelector(`[data-slide="${slideIndex}"]`)) {
                const slideElement = this.createSlideElement(slideIndex, slide.title, slide.content);
                slidesContainer.appendChild(slideElement);
            }
        });
        
        // Create remaining slides programmatically for demonstration
        this.createRemainingSlides();
    }
    
    createSlideElement(index, title, content) {
        const slide = document.createElement('div');
        slide.className = 'slide';
        slide.setAttribute('data-slide', index);
        
        slide.innerHTML = `
            <div class="slide__content">
                <h1 class="slide__title">${title}</h1>
                ${content}
            </div>
        `;
        
        return slide;
    }
    
    createRemainingSlides() {
        const slidesContainer = document.getElementById('slidesContainer');
        const remainingSlides = [
            {
                title: "Familia Oastei Domnului",
                content: `
                    <div class="family-circle">
                        <div class="family-member">👨‍👩‍👧‍👦 Familii</div>
                        <div class="family-member">👥 Tineri</div>
                        <div class="family-member">👶 Copii</div>
                        <div class="family-member">👴👵 Bătrâni</div>
                    </div>
                    <p class="age-content" data-age="8-10">Suntem o familie mare și frumoasă!</p>
                    <p class="age-content" data-age="11-14">Comunitatea noastră este formată din oameni de toate vârstele unidos în credință.</p>
                    <p class="age-content" data-age="15-18">Oastea Domnului este o comunitate intergenerațională bazată pe solidaritatea creștină.</p>
                    <button class="btn btn--primary interactive-btn" data-action="familyStories">📚 Povești din familie</button>
                `
            },
            // Add more slides as needed...
        ];
        
        remainingSlides.forEach((slide, index) => {
            const slideIndex = index + 7; // Continue from where we left off
            if (slideIndex < this.totalSlides && !document.querySelector(`[data-slide="${slideIndex}"]`)) {
                const slideElement = this.createSlideElement(slideIndex, slide.title, slide.content);
                slidesContainer.appendChild(slideElement);
            }
        });
    }
    
    selectAge(age) {
        this.selectedAge = age;
        document.getElementById('ageSelector').classList.add('hidden');
        document.getElementById('ageIndicator').textContent = this.getAgeGroupName(age);
        
        // Show age-appropriate content
        this.updateContentForAge();
        
        // Start presentation
        this.goToSlide(0);
    }
    
    getAgeGroupName(age) {
        const ageGroups = {
            '8-10': 'Micii Ostași',
            '11-14': 'Ostașii în Creștere',
            '15-18': 'Tinerii Ostași'
        };
        return ageGroups[age] || age;
    }
    
    updateContentForAge() {
        // Hide all age-specific content
        document.querySelectorAll('.age-content').forEach(el => {
            el.classList.remove('active');
        });
        
        // Show content for selected age
        document.querySelectorAll(`[data-age="${this.selectedAge}"]`).forEach(el => {
            el.classList.add('active');
        });
    }
    
    showAgeSelector() {
        document.getElementById('ageSelector').classList.remove('hidden');
    }
    
    goToSlide(index) {
        if (index < 0 || index >= this.totalSlides) return;

        // Show loading for smooth transition
        this.showLoading();

        setTimeout(() => {
            // Update current slide
            document.querySelectorAll('.slide').forEach((slide, i) => {
                slide.classList.remove('active', 'prev');
                if (i === index) {
                    slide.classList.add('active');
                } else if (i < index) {
                    slide.classList.add('prev');
                }
            });

            this.currentSlide = index;

            // Track visited slides
            this.visitedSlides.add(index);

            // Update counter
            document.getElementById('currentSlide').textContent = index + 1;

            // Update progress
            this.updateProgress();

            // Update navigation buttons
            document.getElementById('prevBtn').disabled = index === 0;
            document.getElementById('nextBtn').disabled = index === this.totalSlides - 1;

            // Update sidebar active state
            document.querySelectorAll('.sidebar a').forEach(link => {
                link.classList.remove('active');
                if (parseInt(link.dataset.slide) === index) {
                    link.classList.add('active');
                }
            });

            // Update age-appropriate content for current slide
            this.updateContentForAge();

            // Announce to screen readers
            const slideTitle = document.querySelector('.slide.active .slide__title')?.textContent || `Slide ${index + 1}`;
            this.announceSlideChange(index, slideTitle);

            // Check achievements
            this.checkAchievements();

            this.hideLoading();
        }, 150);
    }
    
    nextSlide() {
        if (this.currentSlide < this.totalSlides - 1) {
            this.goToSlide(this.currentSlide + 1);
        }
    }
    
    previousSlide() {
        if (this.currentSlide > 0) {
            this.goToSlide(this.currentSlide - 1);
        }
    }
    
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const presentation = document.getElementById('presentation');
        
        sidebar.classList.toggle('open');
        presentation.classList.toggle('sidebar-open');
    }
    
    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const presentation = document.getElementById('presentation');
        
        sidebar.classList.remove('open');
        presentation.classList.remove('sidebar-open');
    }
    
    handleInteractiveAction(action) {
        // Track interaction for achievements
        this.interactionCount++;
        this.checkAchievements();

        // Add visual feedback
        const button = event.target;
        button.classList.add('success');
        setTimeout(() => button.classList.remove('success'), 600);

        switch (action) {
            case 'playGreeting':
                this.playGreeting();
                break;
            case 'showHistory':
                this.showHistory();
                break;
            case 'bioTimeline':
                this.showBioTimeline();
                break;
            case 'practiceGreeting':
                this.practiceGreeting();
                break;
            case 'symbolsQuiz':
                this.showSymbolsQuiz();
                break;
            case 'churchTour':
                this.showChurchTour();
                break;
            case 'familyStories':
                this.showFamilyStories();
                break;
            case 'finalPrayer':
                this.showFinalPrayer();
                break;
            case 'prayerDemo':
                this.showPrayerDemo();
                break;
            case 'bibleStories':
                this.showBibleStories();
                break;
            case 'gatheringDemo':
                this.showGatheringDemo();
                break;
            case 'songsDemo':
                this.showSongsDemo();
                break;
            case 'jesusStories':
                this.showJesusStories();
                break;
            case 'fastingGuide':
                this.showFastingGuide();
                break;
            case 'sacramentsGuide':
                this.showSacramentsGuide();
                break;
            case 'holidaysCalendar':
                this.showHolidaysCalendar();
                break;
            case 'dailySchedule':
                this.showDailySchedule();
                break;
            case 'schoolScenarios':
                this.showSchoolScenarios();
                break;
            case 'familyActivities':
                this.showFamilyActivities();
                break;
            case 'volunteerProjects':
                this.showVolunteerProjects();
                break;
            case 'ecoProjects':
                this.showEcoProjects();
                break;
            case 'futureVision':
                this.showFutureVision();
                break;
            case 'membershipGuide':
                this.showMembershipGuide();
                break;
            default:
                this.showGenericActivity(action);
        }
    }
    
    playGreeting() {
        this.showModal(`
            <h2>🔊 Salutul Oastei Domnului</h2>
            <div class="greeting-practice">
                <div class="greeting-step">
                    <p><strong>Tu spui:</strong></p>
                    <p class="greeting-line">"Slăvit să fie Domnul!"</p>
                    <button class="btn btn--primary" onclick="this.innerHTML='🔊 Se redă...'">▶️ Ascultă</button>
                </div>
                <div class="greeting-step">
                    <p><strong>Ceilalți răspund:</strong></p>
                    <p class="greeting-line">"În veci, amin!"</p>
                    <button class="btn btn--primary" onclick="this.innerHTML='🔊 Se redă...'">▶️ Ascultă</button>
                </div>
            </div>
            <p class="text-center">
                <em>Acest salut ne amintește că Dumnezeu este în centrul vieții noastre!</em>
            </p>
        `);
    }
    
    showHistory() {
        const ageContent = {
            '8-10': 'În 1923, un preot foarte bun pe nume Iosif Trifa a început Oastea Domnului pentru a-i ajuta pe oameni să îl cunoască mai bine pe Iisus!',
            '11-14': 'Oastea Domnului a fost fondată în 1923 de Părintele Iosif Trifa în Transilvania, ca o mișcare de trezire spirituală în Biserica Ortodoxă Română.',
            '15-18': 'Mișcarea Oastea Domnului, fondată în 1923 de Arhimandritul Iosif Trifa, a reprezentat o încercare de reformă și trezire spirituală în cadrul Bisericii Ortodoxe Române, promovând o spiritualitate personală și comunitară mai vie.'
        };
        
        this.showModal(`
            <h2>📚 Istoria Oastei Domnului</h2>
            <div class="timeline-container">
                <div class="timeline-item">
                    <div class="timeline-year">1923</div>
                    <div class="timeline-content">
                        <h3>Înființarea</h3>
                        <p>${ageContent[this.selectedAge]}</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">1925</div>
                    <div class="timeline-content">
                        <h3>Prima publicație</h3>
                        <p>Apare revista "Iisus Biruitorul"</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">Prezent</div>
                    <div class="timeline-content">
                        <h3>Continuarea misiunii</h3>
                        <p>Mișcarea continuă să inspire credincioșii ortodocși</p>
                    </div>
                </div>
            </div>
        `);
    }
    
    showBioTimeline() {
        this.showModal(`
            <h2>⏰ Viața Părintelui Iosif Trifa</h2>
            <div class="biography-timeline">
                <div class="bio-event">
                    <strong>1888</strong> - Nașterea în Bucium, Alba
                </div>
                <div class="bio-event">
                    <strong>1910</strong> - Hirotonirea ca preot
                </div>
                <div class="bio-event">
                    <strong>1923</strong> - Fondarea Oastei Domnului
                </div>
                <div class="bio-event">
                    <strong>1925</strong> - Începutul publicației "Iisus Biruitorul"
                </div>
                <div class="bio-event">
                    <strong>1938</strong> - Trecerea la cele veșnice
                </div>
            </div>
            <p class="text-center">
                <em>Un om de Dumnezeu care a dedicat viața treziei spirituale!</em>
            </p>
        `);
    }
    
    practiceGreeting() {
        let step = 0;
        const steps = [
            'Ridică mâna dreaptă și spune: "Slăvit să fie Domnul!"',
            'Ascultă răspunsul: "În veci, amin!"',
            'Felicitări! Ai învățat salutul!'
        ];
        
        this.showModal(`
            <h2>🎯 Exersează salutul</h2>
            <div class="practice-container">
                <div class="practice-step" id="practiceStep">
                    <p>${steps[0]}</p>
                    <button class="btn btn--primary" onclick="window.app.nextPracticeStep()">Următorul pas</button>
                </div>
            </div>
        `);
        
        this.practiceStep = 0;
        this.practiceSteps = steps;
    }
    
    nextPracticeStep() {
        this.practiceStep++;
        if (this.practiceStep < this.practiceSteps.length) {
            document.getElementById('practiceStep').innerHTML = `
                <p>${this.practiceSteps[this.practiceStep]}</p>
                <button class="btn btn--primary" onclick="window.app.nextPracticeStep()">
                    ${this.practiceStep === this.practiceSteps.length - 1 ? 'Termină' : 'Următorul pas'}
                </button>
            `;
        } else {
            this.closeModal();
        }
    }
    
    showSymbolsQuiz() {
        this.showModal(`
            <h2>🎯 Quiz despre simboluri</h2>
            <div class="quiz-container">
                <div class="quiz-question">
                    <p>Ce simbolizează crucea ortodoxă?</p>
                    <div class="quiz-options">
                        <button class="btn btn--outline quiz-option" onclick="window.app.answerQuiz(true)">
                            Dragostea și jertfa lui Iisus
                        </button>
                        <button class="btn btn--outline quiz-option" onclick="window.app.answerQuiz(false)">
                            Doar o decorație
                        </button>
                    </div>
                </div>
            </div>
        `);
    }
    
    answerQuiz(correct) {
        const modal = document.getElementById('modalBody');
        if (correct) {
            // Track correct answer for achievements
            this.achievements.set('quiz_correct', true);
            this.checkAchievements();

            modal.innerHTML = `
                <h2>🎉 Corect!</h2>
                <p>Crucea ortodoxă simbolizează dragostea infinită și jertfa lui Iisus pentru noi!</p>
                <button class="btn btn--primary" onclick="window.app.closeModal()">Continuă</button>
            `;

            this.showNotification('success', 'Răspuns corect!', 'Felicitări pentru cunoștințele tale!', '🎉');
        } else {
            modal.innerHTML = `
                <h2>😊 Încearcă din nou!</h2>
                <p>Gândește-te la ce a făcut Iisus pentru noi pe cruce...</p>
                <button class="btn btn--secondary" onclick="window.app.showSymbolsQuiz()">Încearcă din nou</button>
            `;

            this.showNotification('info', 'Încearcă din nou', 'Nu te descuraja, învățarea este un proces!', '💪');
        }
    }
    
    showChurchTour() {
        this.showModal(`
            <h2>🏛️ Turul bisericii ortodoxe</h2>
            <div class="church-tour">
                <div class="tour-section">
                    <h3>⛪ Nartexul</h3>
                    <p>Locul unde ne pregătim pentru rugăciune</p>
                </div>
                <div class="tour-section">
                    <h3>🕯️ Naosul</h3>
                    <p>Aici stau credincioșii în timpul slujbei</p>
                </div>
                <div class="tour-section">
                    <h3>✨ Altarul</h3>
                    <p>Locul cel mai sfânt, unde se oficiază Euharistia</p>
                </div>
            </div>
            <p class="text-center">
                <em>Fiecare parte a bisericii are o însemnătate spirituală specială!</em>
            </p>
        `);
    }
    
    showFamilyStories() {
        this.showModal(`
            <h2>📚 Povești din familia Oastei Domnului</h2>
            <div class="family-stories">
                <div class="story">
                    <h3>👨‍👩‍👧‍👦 Familia Popescu</h3>
                    <p>"Prin Oastea Domnului am învățat să ne rugăm împreună în fiecare seară."</p>
                </div>
                <div class="story">
                    <h3>👥 Grupul de tineri</h3>
                    <p>"Ne întâlnim săptămânal pentru a studia Biblia și a cânta cântece duhovnicești."</p>
                </div>
                <div class="story">
                    <h3>👶 Copiii din Oaste</h3>
                    <p>"Învățăm despre Iisus prin jocuri și activități frumoase!"</p>
                </div>
            </div>
        `);
    }
    
    showFinalPrayer() {
        this.showModal(`
            <h2>🙏 Rugăciune finală</h2>
            <div class="final-prayer">
                <div class="orthodox-cross">✠</div>
                <div class="prayer-text">
                    <p><em>"Doamne Iisuse Hristoste, Fiul lui Dumnezeu, miluiește-ne pe noi păcătoșii și ajută-ne să fim ostași buni în Oastea Ta sfântă."</em></p>
                </div>
                <div class="prayer-response">
                    <p><strong>"Slăvit să fie Domnul!"</strong></p>
                    <p><strong>"În veci, amin!"</strong></p>
                </div>
                <button class="btn btn--primary" onclick="this.innerHTML='🙏 Se rostește rugăciunea...'">
                    🔊 Rostește rugăciunea
                </button>
            </div>
        `);
    }

    showPrayerDemo() {
        this.showModal(`
            <h2>🙏 Demonstrație de rugăciune</h2>
            <div class="prayer-demo-modal">
                <div class="demo-step">
                    <h3>1. Pregătirea</h3>
                    <p>Ne liniștim și ne concentrăm asupra lui Dumnezeu</p>
                </div>
                <div class="demo-step">
                    <h3>2. Semnul crucii</h3>
                    <p>Ne facem semnul sfintei cruci cu evlavie</p>
                </div>
                <div class="demo-step">
                    <h3>3. Rugăciunea</h3>
                    <p>"Tatăl nostru care ești în ceruri..."</p>
                </div>
                <div class="demo-step">
                    <h3>4. Mulțumirea</h3>
                    <p>Îi mulțumim lui Dumnezeu pentru toate binefacerile</p>
                </div>
            </div>
            <button class="btn btn--primary" onclick="this.innerHTML='🙏 Se demonstrează...'">
                ▶️ Începe demonstrația
            </button>
        `);
    }

    showBibleStories() {
        const stories = {
            '8-10': [
                { title: "Noe și arca", description: "Povestea despre ascultarea de Dumnezeu" },
                { title: "David și Goliat", description: "Curajul celui mic cu credință mare" },
                { title: "Iisus și copiii", description: "Dragostea lui Iisus pentru copii" }
            ],
            '11-14': [
                { title: "Parabola semănătorului", description: "Despre primirea Cuvântului lui Dumnezeu" },
                { title: "Înmulțirea pâinilor", description: "Puterea și mila lui Iisus" },
                { title: "Învierea lui Lazăr", description: "Iisus este învierea și viața" }
            ],
            '15-18': [
                { title: "Fiul risipitor", description: "Parabola despre iertarea divină" },
                { title: "Samariteanul milostiv", description: "Exemplul iubirii față de aproapele" },
                { title: "Patimile și Învierea", description: "Centrul credinței creștine" }
            ]
        };

        const ageStories = stories[this.selectedAge] || stories['11-14'];
        let storiesHtml = ageStories.map(story => `
            <div class="bible-story">
                <h4>${story.title}</h4>
                <p>${story.description}</p>
            </div>
        `).join('');

        this.showModal(`
            <h2>📚 Povești biblice</h2>
            <div class="bible-stories-container">
                ${storiesHtml}
            </div>
            <p class="text-center">
                <em>Biblia este plină de povești care ne învață despre dragostea lui Dumnezeu!</em>
            </p>
        `);
    }

    showGatheringDemo() {
        this.showModal(`
            <h2>👥 Cum se desfășoară o adunare frățească</h2>
            <div class="gathering-demo">
                <div class="demo-timeline">
                    <div class="timeline-item">
                        <strong>19:00</strong> - Salutul și rugăciunea de deschidere
                    </div>
                    <div class="timeline-item">
                        <strong>19:15</strong> - Cântece duhovnicești
                    </div>
                    <div class="timeline-item">
                        <strong>19:30</strong> - Studiul biblic
                    </div>
                    <div class="timeline-item">
                        <strong>20:00</strong> - Împărtășirea experiențelor
                    </div>
                    <div class="timeline-item">
                        <strong>20:15</strong> - Rugăciuni comune
                    </div>
                    <div class="timeline-item">
                        <strong>20:30</strong> - Binecuvântarea finală
                    </div>
                </div>
            </div>
            <p class="text-center">
                <em>Fiecare adunare este o întâlnire specială cu Dumnezeu și cu frații!</em>
            </p>
        `);
    }

    showSongsDemo() {
        this.showModal(`
            <h2>🎵 Cântecele noastre duhovnicești</h2>
            <div class="songs-demo">
                <div class="song-demo">
                    <h3>"Iisus Biruitorul"</h3>
                    <div class="song-lyrics">
                        <p><em>Iisus Biruitorul, Iisus Mântuitorul,<br>
                        Tu ești speranța noastră, Tu ești dragostea!</em></p>
                    </div>
                    <button class="btn btn--outline" onclick="this.innerHTML='🎵 Se cântă...'">🔊 Ascultă</button>
                </div>
                <div class="song-demo">
                    <h3>"Slăvit să fie Domnul"</h3>
                    <div class="song-lyrics">
                        <p><em>Slăvit să fie Domnul în veci și în veci,<br>
                        Numele Lui să fie binecuvântat!</em></p>
                    </div>
                    <button class="btn btn--outline" onclick="this.innerHTML='🎵 Se cântă...'">🔊 Ascultă</button>
                </div>
            </div>
            <p class="text-center">
                <em>Prin cântec ne exprimăm bucuria și credința!</em>
            </p>
        `);
    }

    showJesusStories() {
        const stories = {
            '8-10': [
                "Iisus și copiii - cum îi iubea pe cei mici",
                "Iisus vindecă pe cei bolnavi - puterea Sa de a ajuta",
                "Iisus merge pe apă - puterea Sa minunată"
            ],
            '11-14': [
                "Botezul lui Iisus - începutul misiunii Sale",
                "Predica de pe munte - învățăturile Sale",
                "Învierea lui Iisus - biruința asupra morții"
            ],
            '15-18': [
                "Întruparea Logosului - misterul Încarnării",
                "Jertfa de pe Cruce - dragostea supremă",
                "Învierea și Înălțarea - împlinirea mântuirii"
            ]
        };

        const ageStories = stories[this.selectedAge] || stories['11-14'];
        let storiesHtml = ageStories.map(story => `
            <div class="jesus-story">
                <p>✨ ${story}</p>
            </div>
        `).join('');

        this.showModal(`
            <h2>✨ Povești despre Iisus</h2>
            <div class="jesus-stories-container">
                ${storiesHtml}
            </div>
            <p class="text-center">
                <em>Iisus este centrul vieții și credinței noastre!</em>
            </p>
        `);
    }

    showFastingGuide() {
        const fastingInfo = {
            '8-10': {
                title: "Postul pentru copii",
                content: "Copiii pot să postească prin a fi mai buni, să se roage mai mult și să mănânce mai puțin dulciuri."
            },
            '11-14': {
                title: "Postul pentru adolescenți",
                content: "Postul înseamnă să ne abținem de la anumite alimente și să ne concentrăm mai mult asupra rugăciunii și faptelor bune."
            },
            '15-18': {
                title: "Postul ortodox",
                content: "Postul este o disciplină spirituală care include abstinența alimentară, intensificarea rugăciunii și a milostiviei."
            }
        };

        const info = fastingInfo[this.selectedAge] || fastingInfo['11-14'];

        this.showModal(`
            <h2>📋 ${info.title}</h2>
            <div class="fasting-guide">
                <p>${info.content}</p>
                <div class="fasting-periods">
                    <h3>Perioadele de post:</h3>
                    <ul>
                        <li>🌟 Postul Crăciunului (40 de zile)</li>
                        <li>✨ Postul Paștelui (7 săptămâni)</li>
                        <li>🕊️ Postul Rusaliilor (o săptămână)</li>
                        <li>⛪ Postul Adormirii Maicii Domnului (2 săptămâni)</li>
                    </ul>
                </div>
            </div>
            <p class="text-center">
                <em>Postul ne ajută să ne apropiem de Dumnezeu!</em>
            </p>
        `);
    }

    showSacramentsGuide() {
        this.showModal(`
            <h2>✨ Ghidul Sfintelor Taine</h2>
            <div class="sacraments-guide">
                <div class="sacrament-item">
                    <h3>💧 Botezul</h3>
                    <p>Prima Taină prin care devenim copii ai lui Dumnezeu</p>
                </div>
                <div class="sacrament-item">
                    <h3>🕯️ Mirungerea</h3>
                    <p>Primim darurile Duhului Sfânt</p>
                </div>
                <div class="sacrament-item">
                    <h3>🍞 Euharistia</h3>
                    <p>Ne unim cu Hristos prin Sfânta Împărtășanie</p>
                </div>
                <div class="sacrament-item">
                    <h3>🙏 Spovedania</h3>
                    <p>Primim iertarea păcatelor</p>
                </div>
                <div class="sacrament-item">
                    <h3>💒 Cununia</h3>
                    <p>Binecuvântarea căsătoriei creștine</p>
                </div>
                <div class="sacrament-item">
                    <h3>✋ Preoția</h3>
                    <p>Chemarea la slujirea altarului</p>
                </div>
                <div class="sacrament-item">
                    <h3>🛢️ Maslul</h3>
                    <p>Vindecarea sufletească și trupească</p>
                </div>
            </div>
            <p class="text-center">
                <em>Prin Sfintele Taine primim harul lui Dumnezeu!</em>
            </p>
        `);
    }

    showHolidaysCalendar() {
        this.showModal(`
            <h2>📅 Calendarul sărbătorilor ortodoxe</h2>
            <div class="holidays-calendar">
                <div class="holiday-season">
                    <h3>🌟 Iarna</h3>
                    <ul>
                        <li>6 ianuarie - Boboteaza</li>
                        <li>25 decembrie - Crăciunul</li>
                        <li>1 ianuarie - Sfântul Vasile</li>
                    </ul>
                </div>
                <div class="holiday-season">
                    <h3>🌸 Primăvara</h3>
                    <ul>
                        <li>25 martie - Buna Vestire</li>
                        <li>Paștele (data variabilă)</li>
                        <li>Rusaliile (50 zile după Paște)</li>
                    </ul>
                </div>
                <div class="holiday-season">
                    <h3>☀️ Vara</h3>
                    <ul>
                        <li>29 iunie - Sfinții Petru și Pavel</li>
                        <li>15 august - Adormirea Maicii Domnului</li>
                        <li>29 august - Tăierea capului Sf. Ioan</li>
                    </ul>
                </div>
                <div class="holiday-season">
                    <h3>🍂 Toamna</h3>
                    <ul>
                        <li>8 septembrie - Nașterea Maicii Domnului</li>
                        <li>14 septembrie - Înălțarea Sfintei Cruci</li>
                        <li>26 octombrie - Sfântul Dumitru</li>
                    </ul>
                </div>
            </div>
        `);
    }

    showDailySchedule() {
        const schedules = {
            '8-10': {
                morning: "Rugăciune scurtă și mulțumire pentru ziua nouă",
                day: "Fapte bune la școală și acasă",
                evening: "Rugăciune de seară cu familia"
            },
            '11-14': {
                morning: "Rugăciunea de dimineață și citirea unui psalm",
                day: "Aplicarea valorilor creștine în activitățile zilnice",
                evening: "Rugăciunea de seară și examenul de conștiință"
            },
            '15-18': {
                morning: "Rugăciunea matinală și meditația asupra Scripturii",
                day: "Mărturisirea credinței prin exemplul personal",
                evening: "Rugăciunea de seară și planificarea zilei următoare"
            }
        };

        const schedule = schedules[this.selectedAge] || schedules['11-14'];

        this.showModal(`
            <h2>📋 Programul zilnic al ostașului</h2>
            <div class="daily-schedule">
                <div class="schedule-time">
                    <h3>🌅 Dimineața</h3>
                    <p>${schedule.morning}</p>
                </div>
                <div class="schedule-time">
                    <h3>☀️ În timpul zilei</h3>
                    <p>${schedule.day}</p>
                </div>
                <div class="schedule-time">
                    <h3>🌙 Seara</h3>
                    <p>${schedule.evening}</p>
                </div>
            </div>
            <p class="text-center">
                <em>Fiecare zi este o oportunitate de a trăi pentru Hristos!</em>
            </p>
        `);
    }

    showSchoolScenarios() {
        this.showModal(`
            <h2>🎭 Situații la școală</h2>
            <div class="school-scenarios">
                <div class="scenario">
                    <h3>Situația 1: Colegul care copiază</h3>
                    <p><strong>Ce faci?</strong> Îl ajuți să înțeleagă lecția în loc să îl lași să copieze.</p>
                </div>
                <div class="scenario">
                    <h3>Situația 2: Cineva este batjocorit</h3>
                    <p><strong>Ce faci?</strong> Îl aperi și încerci să aduci pace între colegi.</p>
                </div>
                <div class="scenario">
                    <h3>Situația 3: Tentația de a minți</h3>
                    <p><strong>Ce faci?</strong> Spui adevărul cu curaj și îți asumi responsabilitatea.</p>
                </div>
            </div>
            <p class="text-center">
                <em>La școală putem fi lumina lui Hristos pentru ceilalți!</em>
            </p>
        `);
    }

    showFamilyActivities() {
        this.showModal(`
            <h2>👨‍👩‍👧‍👦 Activități în familie</h2>
            <div class="family-activities">
                <div class="activity-category">
                    <h3>🙏 Activități spirituale</h3>
                    <ul>
                        <li>Rugăciunea comună de seară</li>
                        <li>Citirea Bibliei împreună</li>
                        <li>Participarea la slujbe</li>
                        <li>Discuții despre credință</li>
                    </ul>
                </div>
                <div class="activity-category">
                    <h3>🤝 Activități practice</h3>
                    <ul>
                        <li>Ajutorarea la treburile casnice</li>
                        <li>Grija pentru bunici</li>
                        <li>Proiecte de caritate în familie</li>
                        <li>Vizitarea celor în nevoie</li>
                    </ul>
                </div>
            </div>
            <p class="text-center">
                <em>Familia este prima noastră școală de credință!</em>
            </p>
        `);
    }

    showVolunteerProjects() {
        this.showModal(`
            <h2>🤝 Proiecte de voluntariat</h2>
            <div class="volunteer-projects">
                <div class="project">
                    <h3>🍞 "Pâinea zilnică"</h3>
                    <p>Distribuirea de alimente pentru familiile nevoiașe</p>
                </div>
                <div class="project">
                    <h3>📚 "Ajutor la teme"</h3>
                    <p>Sprijin educațional pentru copiii din familii defavorizate</p>
                </div>
                <div class="project">
                    <h3>👴 "Bunicii noștri"</h3>
                    <p>Vizite și activități pentru persoanele vârstnice</p>
                </div>
                <div class="project">
                    <h3>🏥 "Speranța vindecării"</h3>
                    <p>Activități pentru copiii din spitale</p>
                </div>
            </div>
            <p class="text-center">
                <em>Prin ajutorarea celorlalți ne asemănăm cu Hristos!</em>
            </p>
        `);
    }

    showEcoProjects() {
        this.showModal(`
            <h2>🌱 Proiecte ecologice</h2>
            <div class="eco-projects">
                <div class="eco-project">
                    <h3>🌳 "Pădurea Oastei"</h3>
                    <p>Plantarea de copaci în zonele defrișate</p>
                </div>
                <div class="eco-project">
                    <h3>♻️ "Reciclarea responsabilă"</h3>
                    <p>Campanii de colectare și reciclare a deșeurilor</p>
                </div>
                <div class="eco-project">
                    <h3>🐝 "Grădina lui Dumnezeu"</h3>
                    <p>Crearea de spații verzi și protejarea biodiversității</p>
                </div>
                <div class="eco-project">
                    <h3>💧 "Apa vie"</h3>
                    <p>Protejarea surselor de apă și educația ecologică</p>
                </div>
            </div>
            <p class="text-center">
                <em>Să păzim frumusețea creației lui Dumnezeu!</em>
            </p>
        `);
    }

    showFutureVision() {
        this.showModal(`
            <h2>🔮 Viziunea ta pentru viitor</h2>
            <div class="future-vision">
                <div class="vision-question">
                    <h3>Cum te vezi în Oastea Domnului peste 10 ani?</h3>
                    <div class="vision-options">
                        <div class="vision-option">
                            <h4>👨‍🏫 Educator creștin</h4>
                            <p>Predând și ghidând noile generații</p>
                        </div>
                        <div class="vision-option">
                            <h4>🎵 Conducător de activități</h4>
                            <p>Organizând evenimente și adunări</p>
                        </div>
                        <div class="vision-option">
                            <h4>🤝 Coordonator social</h4>
                            <p>Dezvoltând proiecte de ajutorare</p>
                        </div>
                        <div class="vision-option">
                            <h4>📚 Cercetător teologic</h4>
                            <p>Aprofundând învățăturile ortodoxe</p>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center">
                <em>Dumnezeu are un plan minunat pentru fiecare dintre noi!</em>
            </p>
        `);
    }

    showMembershipGuide() {
        this.showModal(`
            <h2>📋 Ghidul membrului Oastea Domnului</h2>
            <div class="membership-guide">
                <div class="membership-step">
                    <h3>1️⃣ Participarea</h3>
                    <p>Participă regulat la adunările frățești și activitățile comunității</p>
                </div>
                <div class="membership-step">
                    <h3>2️⃣ Catehizarea</h3>
                    <p>Urmează cursurile de catehism și aprofundează cunoștințele biblice</p>
                </div>
                <div class="membership-step">
                    <h3>3️⃣ Angajamentul</h3>
                    <p>Exprimă-ți dorința de a trăi ca ostași al lui Hristos</p>
                </div>
                <div class="membership-step">
                    <h3>4️⃣ Binecuvântarea</h3>
                    <p>Primește binecuvântarea părintelui duhovnic și a comunității</p>
                </div>
                <div class="membership-responsibilities">
                    <h3>Responsabilitățile membrului:</h3>
                    <ul>
                        <li>Participarea la viața spirituală a comunității</li>
                        <li>Susținerea activităților caritabile</li>
                        <li>Mărturisirea credinței prin exemplul personal</li>
                        <li>Contribuția la dezvoltarea mișcării</li>
                    </ul>
                </div>
            </div>
        `);
    }
    
    showGenericActivity(action) {
        this.showModal(`
            <h2>📱 Activitate interactivă</h2>
            <p>Această activitate (${action}) va fi dezvoltată în versiunea completă.</p>
            <p>Mulțumim pentru răbdare!</p>
        `);
    }
    
    showModal(content) {
        document.getElementById('modalBody').innerHTML = content;
        document.getElementById('interactiveModal').classList.add('open');
    }
    
    closeModal() {
        document.getElementById('interactiveModal').classList.remove('open');
    }
    
    restartPresentation() {
        this.currentSlide = 0;
        this.selectedAge = null;
        this.closeModal();
        this.closeSidebar();
        this.showAgeSelector();
    }

    // Achievement System
    initializeAchievements() {
        const achievementDefinitions = [
            { id: 'first_slide', icon: '🎯', title: 'Primul pas', description: 'Ai vizualizat primul slide', condition: () => this.visitedSlides.has(0) },
            { id: 'section_complete', icon: '📚', title: 'Secțiune completă', description: 'Ai completat o secțiune întreagă', condition: () => this.hasCompletedSection() },
            { id: 'interactive_explorer', icon: '🎮', title: 'Explorator interactiv', description: 'Ai folosit 5 elemente interactive', condition: () => this.interactionCount >= 5 },
            { id: 'quiz_master', icon: '🧠', title: 'Maestru quiz', description: 'Ai răspuns corect la un quiz', condition: () => this.achievements.has('quiz_correct') },
            { id: 'presentation_complete', icon: '🏆', title: 'Prezentare completă', description: 'Ai vizualizat toate slide-urile', condition: () => this.visitedSlides.size === this.totalSlides },
            { id: 'speed_reader', icon: '⚡', title: 'Cititor rapid', description: 'Ai completat prezentarea în mai puțin de 10 minute', condition: () => this.getSessionDuration() < 600000 && this.visitedSlides.size === this.totalSlides }
        ];

        achievementDefinitions.forEach(achievement => {
            this.achievements.set(achievement.id, { ...achievement, earned: false, earnedAt: null });
        });

        this.updateAchievementsDisplay();
    }

    checkAchievements() {
        let newAchievements = [];

        this.achievements.forEach((achievement, id) => {
            if (!achievement.earned && achievement.condition()) {
                achievement.earned = true;
                achievement.earnedAt = Date.now();
                newAchievements.push(achievement);
            }
        });

        if (newAchievements.length > 0) {
            newAchievements.forEach(achievement => {
                this.showNotification('success', achievement.title, achievement.description, achievement.icon);
            });
            this.updateAchievementsDisplay();
        }
    }

    hasCompletedSection() {
        return this.sections.some(section => {
            for (let i = section.start; i <= section.end; i++) {
                if (!this.visitedSlides.has(i)) return false;
            }
            return true;
        });
    }

    getSessionDuration() {
        return Date.now() - this.startTime;
    }

    updateAchievementsDisplay() {
        const earnedCount = Array.from(this.achievements.values()).filter(a => a.earned).length;
        document.getElementById('achievementCount').textContent = earnedCount;

        const achievementsList = document.getElementById('achievementsList');
        achievementsList.innerHTML = '';

        this.achievements.forEach(achievement => {
            const item = document.createElement('div');
            item.className = `achievement-item ${achievement.earned ? 'earned' : 'locked'}`;
            item.innerHTML = `
                <div class="achievement-icon">${achievement.icon}</div>
                <div class="achievement-info">
                    <div class="achievement-title">${achievement.title}</div>
                    <div class="achievement-description">${achievement.description}</div>
                </div>
            `;
            achievementsList.appendChild(item);
        });
    }

    toggleAchievements() {
        const dropdown = document.getElementById('achievementsDropdown');
        dropdown.classList.toggle('open');
    }

    closeAchievements() {
        const dropdown = document.getElementById('achievementsDropdown');
        dropdown.classList.remove('open');
    }

    // Notification System
    showNotification(type, title, message, icon = '') {
        const container = document.getElementById('notificationContainer');
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        notification.innerHTML = `
            <div class="notification-icon">${icon}</div>
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" aria-label="Închide notificarea">×</button>
        `;

        container.appendChild(notification);

        // Show notification
        setTimeout(() => notification.classList.add('show'), 100);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => container.removeChild(notification), 300);
        }, 5000);

        // Manual close
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.classList.remove('show');
            setTimeout(() => container.removeChild(notification), 300);
        });
    }

    // Enhanced Progress Tracking
    updateProgress() {
        const progress = ((this.currentSlide + 1) / this.totalSlides) * 100;
        document.getElementById('progressFill').style.width = `${progress}%`;

        // Update section breadcrumb
        const currentSection = this.sections.find(section =>
            this.currentSlide >= section.start && this.currentSlide <= section.end
        );
        if (currentSection) {
            document.getElementById('currentSection').textContent = currentSection.name;
        }
    }

    // Accessibility Improvements
    setupAccessibility() {
        // Add skip link
        const skipLink = document.createElement('a');
        skipLink.href = '#slidesContainer';
        skipLink.className = 'skip-link';
        skipLink.textContent = 'Sari la conținutul principal';
        document.body.insertBefore(skipLink, document.body.firstChild);

        // Announce slide changes to screen readers
        this.announceSlideChange = (slideNumber, title) => {
            const announcement = `Slide ${slideNumber + 1} din ${this.totalSlides}: ${title}`;
            const announcer = document.createElement('div');
            announcer.setAttribute('aria-live', 'polite');
            announcer.setAttribute('aria-atomic', 'true');
            announcer.className = 'sr-only';
            announcer.textContent = announcement;
            document.body.appendChild(announcer);
            setTimeout(() => document.body.removeChild(announcer), 1000);
        };
    }

    // Touch Events for Mobile
    setupTouchEvents() {
        let startX = 0;
        let startY = 0;
        const slidesContainer = document.getElementById('slidesContainer');

        slidesContainer.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        }, { passive: true });

        slidesContainer.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;

            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;

            const diffX = startX - endX;
            const diffY = startY - endY;

            // Minimum swipe distance
            if (Math.abs(diffX) < 50 && Math.abs(diffY) < 50) return;

            // Horizontal swipe is more significant
            if (Math.abs(diffX) > Math.abs(diffY)) {
                if (diffX > 0) {
                    this.nextSlide(); // Swipe left - next slide
                } else {
                    this.previousSlide(); // Swipe right - previous slide
                }
            }

            startX = 0;
            startY = 0;
        }, { passive: true });
    }

    // Loading States
    showLoading() {
        document.getElementById('loadingOverlay').classList.add('show');
    }

    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('show');
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new OastePresentationApp();
});

// Add some additional CSS dynamically for better styling
const additionalStyles = `
    .symbols-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-24);
        margin: var(--space-24) 0;
    }
    
    .symbol-item {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-24);
        border-radius: var(--radius-lg);
        text-align: center;
        border: 2px solid rgba(255, 215, 0, 0.3);
    }
    
    .symbol {
        font-size: 3rem;
        color: #ffd700;
        margin-bottom: var(--space-16);
    }
    
    .church-visual {
        font-size: 4rem;
        margin: var(--space-24) 0;
    }
    
    .church-elements {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        gap: var(--space-16);
        margin: var(--space-24) 0;
    }
    
    .element {
        background: rgba(255, 215, 0, 0.2);
        padding: var(--space-12) var(--space-16);
        border-radius: var(--radius-lg);
        font-size: var(--font-size-lg);
    }
    
    .family-circle {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-16);
        margin: var(--space-24) 0;
    }
    
    .family-member {
        background: rgba(255, 215, 0, 0.2);
        padding: var(--space-16);
        border-radius: var(--radius-lg);
        text-align: center;
        font-size: var(--font-size-lg);
    }
    
    .quiz-container {
        text-align: center;
    }
    
    .quiz-options {
        display: flex;
        flex-direction: column;
        gap: var(--space-12);
        margin-top: var(--space-16);
    }
    
    .quiz-option {
        padding: var(--space-12) var(--space-16);
    }
    
    .biography-timeline {
        margin: var(--space-24) 0;
    }
    
    .bio-event {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-12) var(--space-16);
        margin-bottom: var(--space-8);
        border-radius: var(--radius-sm);
        border-left: 4px solid #ffd700;
    }
    
    .church-tour {
        margin: var(--space-24) 0;
    }
    
    .tour-section {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        margin-bottom: var(--space-12);
        border-radius: var(--radius-lg);
    }
    
    .family-stories {
        margin: var(--space-24) 0;
    }
    
    .story {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        margin-bottom: var(--space-12);
        border-radius: var(--radius-lg);
        border-left: 4px solid #1e3a8a;
    }
    
    .final-prayer {
        text-align: center;
    }
    
    .prayer-text {
        background: rgba(30, 58, 138, 0.1);
        padding: var(--space-24);
        border-radius: var(--radius-lg);
        margin: var(--space-24) 0;
        font-size: var(--font-size-lg);
    }
    
    .prayer-response {
        margin: var(--space-24) 0;
    }
    
    .prayer-response p {
        color: #1e3a8a;
        font-size: var(--font-size-xl);
        margin: var(--space-8) 0;
    }
    
    .practice-container {
        text-align: center;
        padding: var(--space-24);
    }
    
    .practice-step p {
        font-size: var(--font-size-lg);
        margin-bottom: var(--space-16);
        color: #1e3a8a;
    }

    /* New interactive elements styles */
    .prayer-demo-modal .demo-step {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        margin-bottom: var(--space-12);
        border-radius: var(--radius-lg);
        border-left: 4px solid #1e3a8a;
    }

    .bible-stories-container .bible-story,
    .jesus-stories-container .jesus-story {
        background: rgba(255, 215, 0, 0.1);
        padding: var(--space-12) var(--space-16);
        margin-bottom: var(--space-8);
        border-radius: var(--radius-sm);
        border-left: 3px solid #ffd700;
    }

    .songs-demo .song-demo {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        margin-bottom: var(--space-16);
        border-radius: var(--radius-lg);
        text-align: center;
    }

    .song-lyrics {
        font-style: italic;
        color: #1e3a8a;
        margin: var(--space-12) 0;
    }

    .gathering-demo .demo-timeline .timeline-item {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-12) var(--space-16);
        margin-bottom: var(--space-8);
        border-radius: var(--radius-sm);
        border-left: 4px solid #ffd700;
    }

    .fasting-guide .fasting-periods ul {
        list-style: none;
        padding: 0;
    }

    .fasting-guide .fasting-periods li {
        background: rgba(255, 215, 0, 0.1);
        padding: var(--space-8) var(--space-12);
        margin-bottom: var(--space-4);
        border-radius: var(--radius-sm);
    }

    .sacraments-guide .sacrament-item {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        margin-bottom: var(--space-12);
        border-radius: var(--radius-lg);
        border-left: 4px solid #1e3a8a;
    }

    .holidays-calendar .holiday-season {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        margin-bottom: var(--space-16);
        border-radius: var(--radius-lg);
    }

    .holidays-calendar ul {
        list-style: none;
        padding: 0;
    }

    .holidays-calendar li {
        padding: var(--space-4) 0;
        border-bottom: 1px solid rgba(30, 58, 138, 0.1);
    }

    .daily-schedule .schedule-time {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        margin-bottom: var(--space-12);
        border-radius: var(--radius-lg);
        border-left: 4px solid #ffd700;
    }

    .school-scenarios .scenario,
    .volunteer-projects .project,
    .eco-projects .eco-project {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        margin-bottom: var(--space-12);
        border-radius: var(--radius-lg);
        border-left: 4px solid #1e3a8a;
    }

    .family-activities .activity-category {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        margin-bottom: var(--space-16);
        border-radius: var(--radius-lg);
    }

    .family-activities ul {
        list-style: none;
        padding: 0;
    }

    .family-activities li {
        padding: var(--space-4) 0;
        padding-left: var(--space-16);
        position: relative;
    }

    .family-activities li::before {
        content: "❤️";
        position: absolute;
        left: 0;
    }

    .future-vision .vision-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-16);
        margin-top: var(--space-16);
    }

    .vision-option {
        background: rgba(255, 215, 0, 0.1);
        padding: var(--space-16);
        border-radius: var(--radius-lg);
        text-align: center;
        border: 2px solid rgba(255, 215, 0, 0.3);
        transition: all var(--duration-normal) var(--ease-standard);
    }

    .vision-option:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);
    }

    .membership-guide .membership-step {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        margin-bottom: var(--space-12);
        border-radius: var(--radius-lg);
        border-left: 4px solid #1e3a8a;
    }

    .membership-responsibilities {
        background: rgba(255, 215, 0, 0.1);
        padding: var(--space-16);
        border-radius: var(--radius-lg);
        margin-top: var(--space-16);
    }

    .membership-responsibilities ul {
        list-style: none;
        padding: 0;
    }

    .membership-responsibilities li {
        padding: var(--space-4) 0;
        padding-left: var(--space-16);
        position: relative;
    }

    .membership-responsibilities li::before {
        content: "⚔️";
        position: absolute;
        left: 0;
    }

    /* Prayer demo styles */
    .prayer-steps {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-16);
        margin: var(--space-24) 0;
    }

    .prayer-step {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        border-radius: var(--radius-lg);
        text-align: center;
        border: 2px solid rgba(255, 215, 0, 0.3);
    }

    .step-icon {
        font-size: 2rem;
        margin-bottom: var(--space-8);
    }

    /* Bible info styles */
    .bible-info, .church-info, .gathering-info, .songs-info, .jesus-info,
    .fasting-info, .sacraments-info, .holidays-info, .daily-life,
    .school-info, .family-info, .helping-info, .nature-info, .future-info, .membership-info {
        display: flex;
        align-items: center;
        gap: var(--space-24);
        margin: var(--space-24) 0;
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-24);
        border-radius: var(--radius-lg);
    }

    .bible-visual, .church-visual, .gathering-visual, .songs-visual, .jesus-visual,
    .fasting-visual, .sacraments-visual, .holidays-visual, .daily-visual,
    .school-visual, .family-visual, .helping-visual, .nature-visual, .future-visual, .membership-visual {
        font-size: 4rem;
        min-width: 80px;
        text-align: center;
    }

    .bible-description, .church-description, .gathering-description, .songs-description, .jesus-description,
    .fasting-description, .sacraments-description, .holidays-description, .daily-description,
    .school-description, .family-description, .helping-description, .nature-description, .future-description, .membership-description {
        flex: 1;
    }

    /* Grid layouts for various elements */
    .bible-parts, .church-elements, .gathering-activities, .jesus-qualities,
    .fasting-benefits, .sacraments-list, .major-holidays, .daily-practices,
    .school-values, .family-values, .helping-ways, .nature-actions, .future-roles, .membership-steps {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: var(--space-16);
        margin: var(--space-24) 0;
    }

    .bible-part, .element, .activity, .quality, .benefit, .sacrament, .holiday,
    .practice, .value, .way, .action, .role, .step {
        background: rgba(255, 215, 0, 0.2);
        padding: var(--space-12) var(--space-16);
        border-radius: var(--radius-lg);
        text-align: center;
        font-size: var(--font-size-md);
        font-weight: var(--font-weight-medium);
    }

    /* Popular songs styles */
    .popular-songs {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-16);
        margin: var(--space-24) 0;
    }

    .song-item {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        border-radius: var(--radius-lg);
        text-align: center;
        border: 2px solid rgba(255, 215, 0, 0.3);
    }

    .song-item h4 {
        color: #1e3a8a;
        margin-bottom: var(--space-8);
    }
`;

// Add the additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);