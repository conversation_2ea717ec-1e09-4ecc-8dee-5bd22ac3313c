
:root {
  /* Enhanced Color Palette - Orthodox Inspired */
  --color-background: linear-gradient(135deg, #0f1419 0%, #1a2332 50%, #2d3748 100%);
  --color-surface: rgba(255, 255, 255, 0.95);
  --color-surface-glass: rgba(255, 255, 255, 0.1);
  --color-surface-elevated: rgba(255, 255, 255, 0.98);
  --color-text: rgba(26, 32, 44, 1);
  --color-text-light: rgba(255, 255, 255, 0.95);
  --color-text-secondary: rgba(74, 85, 104, 1);
  --color-text-muted: rgba(160, 174, 192, 1);

  /* Orthodox Gold & Blue Palette */
  --color-primary: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  --color-primary-solid: #1e3a8a;
  --color-primary-light: #3b82f6;
  --color-primary-hover: linear-gradient(135deg, #1e40af 0%, #2563eb 100%);
  --color-primary-active: linear-gradient(135deg, #1d4ed8 0%, #1d4ed8 100%);

  --color-gold: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
  --color-gold-solid: #fbbf24;
  --color-gold-light: #fef3c7;
  --color-gold-hover: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);

  --color-secondary: rgba(99, 102, 241, 0.1);
  --color-secondary-hover: rgba(99, 102, 241, 0.2);
  --color-secondary-active: rgba(99, 102, 241, 0.3);

  --color-border: rgba(226, 232, 240, 0.8);
  --color-border-light: rgba(255, 255, 255, 0.2);
  --color-border-accent: rgba(251, 191, 36, 0.3);

  --color-btn-primary-text: rgba(255, 255, 255, 1);
  --color-card-border: rgba(226, 232, 240, 0.6);
  --color-card-border-inner: rgba(226, 232, 240, 0.4);

  /* Status Colors */
  --color-error: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  --color-success: linear-gradient(135deg, #059669 0%, #10b981 100%);
  --color-warning: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
  --color-info: linear-gradient(135deg, #0284c7 0%, #0ea5e9 100%);

  --color-focus-ring: rgba(99, 102, 241, 0.6);
  --color-select-caret: rgba(26, 32, 44, 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Enhanced Animation System */
  --duration-instant: 100ms;
  --duration-fast: 200ms;
  --duration-normal: 350ms;
  --duration-slow: 500ms;
  --duration-slower: 750ms;

  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --ease-back: cubic-bezier(0.34, 1.56, 0.64, 1);
  --ease-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* Glass Morphism Effects */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  --glass-backdrop: blur(20px);

  /* Neon Glow Effects */
  --glow-primary: 0 0 20px rgba(30, 58, 138, 0.5), 0 0 40px rgba(30, 58, 138, 0.3), 0 0 60px rgba(30, 58, 138, 0.1);
  --glow-gold: 0 0 20px rgba(251, 191, 36, 0.5), 0 0 40px rgba(251, 191, 36, 0.3), 0 0 60px rgba(251, 191, 36, 0.1);
  --glow-success: 0 0 20px rgba(16, 185, 129, 0.5), 0 0 40px rgba(16, 185, 129, 0.3);
  --glow-error: 0 0 20px rgba(239, 68, 68, 0.5), 0 0 40px rgba(239, 68, 68, 0.3);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: rgba(31, 33, 33, 1);
    --color-surface: rgba(38, 40, 40, 1);
    --color-text: rgba(245, 245, 245, 1);
    --color-text-secondary: rgba(167, 169, 169, 0.7);
    --color-primary: rgba(50, 184, 198, 1);
    --color-primary-hover: rgba(45, 166, 178, 1);
    --color-primary-active: rgba(41, 150, 161, 1);
    --color-secondary: rgba(119, 124, 124, 0.15);
    --color-secondary-hover: rgba(119, 124, 124, 0.25);
    --color-secondary-active: rgba(119, 124, 124, 0.3);
    --color-border: rgba(119, 124, 124, 0.3);
    --color-error: rgba(255, 84, 89, 1);
    --color-success: rgba(50, 184, 198, 1);
    --color-warning: rgba(230, 129, 97, 1);
    --color-info: rgba(167, 169, 169, 1);
    --color-focus-ring: rgba(50, 184, 198, 0.4);
    --color-btn-primary-text: rgba(19, 52, 59, 1);
    --color-card-border: rgba(119, 124, 124, 0.2);
    --color-card-border-inner: rgba(119, 124, 124, 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(119, 124, 124, 0.2);
    --color-border-secondary: rgba(119, 124, 124, 0.2);
    --color-select-caret: rgba(245, 245, 245, 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: 50, 184, 198;
    --color-error-rgb: 255, 84, 89;
    --color-warning-rgb: 230, 129, 97;
    --color-info-rgb: 167, 169, 169;
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  --color-background: rgba(31, 33, 33, 1);
  --color-surface: rgba(38, 40, 40, 1);
  --color-text: rgba(245, 245, 245, 1);
  --color-text-secondary: rgba(167, 169, 169, 0.7);
  --color-primary: rgba(50, 184, 198, 1);
  --color-primary-hover: rgba(45, 166, 178, 1);
  --color-primary-active: rgba(41, 150, 161, 1);
  --color-secondary: rgba(119, 124, 124, 0.15);
  --color-secondary-hover: rgba(119, 124, 124, 0.25);
  --color-secondary-active: rgba(119, 124, 124, 0.3);
  --color-border: rgba(119, 124, 124, 0.3);
  --color-error: rgba(255, 84, 89, 1);
  --color-success: rgba(50, 184, 198, 1);
  --color-warning: rgba(230, 129, 97, 1);
  --color-info: rgba(167, 169, 169, 1);
  --color-focus-ring: rgba(50, 184, 198, 0.4);
  --color-btn-primary-text: rgba(19, 52, 59, 1);
  --color-card-border: rgba(119, 124, 124, 0.15);
  --color-card-border-inner: rgba(119, 124, 124, 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(119, 124, 124, 0.2);
  --color-select-caret: rgba(245, 245, 245, 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: 50, 184, 198;
  --color-error-rgb: 255, 84, 89;
  --color-warning-rgb: 230, 129, 97;
  --color-info-rgb: 167, 169, 169;
}

[data-color-scheme="light"] {
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;
}

/* Enhanced Base Styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  box-sizing: border-box;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  background: var(--color-background);
  background-attachment: fixed;
  position: relative;
}

/* Animated Background Particles */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(251, 191, 36, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(30, 58, 138, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
  animation: backgroundFloat 20s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes backgroundFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Revolutionary Button System */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-12) var(--space-24);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-bounce);
  border: none;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  backdrop-filter: var(--glass-backdrop);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Animated Background Overlay */
.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--duration-slow) var(--ease-standard);
}

.btn:hover::before {
  left: 100%;
}

/* Ripple Effect */
.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width var(--duration-normal) var(--ease-standard), height var(--duration-normal) var(--ease-standard);
}

.btn:active::after {
  width: 300px;
  height: 300px;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring), var(--glow-primary);
  transform: translateY(-2px);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  border: 2px solid transparent;
  box-shadow: var(--glow-primary), 0 8px 25px rgba(30, 58, 138, 0.3);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--glow-primary), 0 12px 35px rgba(30, 58, 138, 0.4);
}

.btn--primary:active {
  background: var(--color-primary-active);
  transform: translateY(-1px) scale(0.98);
}

.btn--secondary {
  background: var(--color-gold);
  color: var(--color-text);
  border: 2px solid transparent;
  box-shadow: var(--glow-gold), 0 8px 25px rgba(251, 191, 36, 0.3);
}

.btn--secondary:hover {
  background: var(--color-gold-hover);
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--glow-gold), 0 12px 35px rgba(251, 191, 36, 0.4);
}

.btn--secondary:active {
  transform: translateY(-1px) scale(0.98);
}

.btn--outline {
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  color: var(--color-text-light);
  backdrop-filter: var(--glass-backdrop);
  box-shadow: var(--glass-shadow);
}

.btn--outline:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--color-gold-solid);
  transform: translateY(-2px);
  box-shadow: var(--glass-shadow), var(--glow-gold);
}

/* Glowing Success Button */
.btn--success {
  background: var(--color-success);
  color: white;
  box-shadow: var(--glow-success);
  animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
  0%, 100% { box-shadow: var(--glow-success); }
  50% { box-shadow: var(--glow-success), 0 0 30px rgba(16, 185, 129, 0.6); }
}

/* Error Button with Shake */
.btn--error {
  background: var(--color-error);
  color: white;
  box-shadow: var(--glow-error);
}

.btn--error:hover {
  animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* Revolutionary Presentation Container */
.presentation-container {
  font-family: var(--font-family-base);
  height: 100vh;
  overflow: hidden;
  background: var(--color-background);
  position: relative;
  backdrop-filter: blur(20px);
}

/* Floating Particles Animation */
.presentation-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(251, 191, 36, 0.1) 0%, transparent 20%),
    radial-gradient(circle at 90% 80%, rgba(30, 58, 138, 0.1) 0%, transparent 20%),
    radial-gradient(circle at 50% 50%, rgba(99, 102, 241, 0.05) 0%, transparent 20%);
  animation: particleFloat 15s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-20px) rotate(2deg);
    opacity: 0.6;
  }
  66% {
    transform: translateY(10px) rotate(-2deg);
    opacity: 0.4;
  }
}

/* Spectacular Age Selector */
.age-selector {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 30% 70%, rgba(30, 58, 138, 0.9) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(251, 191, 36, 0.8) 0%, transparent 50%),
    linear-gradient(135deg, rgba(15, 20, 25, 0.95) 0%, rgba(26, 35, 50, 0.95) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(20px);
  animation: ageSelector-fadeIn 1s var(--ease-bounce);
}

@keyframes ageSelector-fadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.age-selector__content {
  text-align: center;
  color: white;
  max-width: 700px;
  padding: var(--space-32);
  background: var(--glass-bg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-backdrop);
  box-shadow: var(--glass-shadow), var(--glow-primary);
  animation: contentFloat 3s ease-in-out infinite;
}

@keyframes contentFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.age-selector__content h2 {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--space-32);
  background: var(--color-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(251, 191, 36, 0.5);
  animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% { text-shadow: 0 0 30px rgba(251, 191, 36, 0.5); }
  100% { text-shadow: 0 0 50px rgba(251, 191, 36, 0.8), 0 0 70px rgba(251, 191, 36, 0.4); }
}

.age-buttons {
  display: flex;
  gap: var(--space-32);
  justify-content: center;
  flex-wrap: wrap;
  margin-top: var(--space-24);
}

.age-btn {
  padding: var(--space-24) var(--space-32);
  font-size: var(--font-size-xl);
  background: var(--color-gold);
  color: var(--color-text);
  border: 3px solid transparent;
  border-radius: var(--radius-lg);
  min-width: 200px;
  min-height: 120px;
  transition: all var(--duration-normal) var(--ease-bounce);
  font-weight: var(--font-weight-bold);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  box-shadow: var(--glow-gold), 0 10px 30px rgba(251, 191, 36, 0.3);
  backdrop-filter: blur(10px);
}

.age-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--duration-slow) var(--ease-standard);
}

.age-btn:hover::before {
  left: 100%;
}

.age-btn:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: var(--glow-gold), 0 20px 50px rgba(251, 191, 36, 0.5);
  border-color: rgba(255, 255, 255, 0.5);
  background: var(--color-gold-hover);
}

.age-btn:active {
  transform: translateY(-4px) scale(1.02);
}

.age-btn small {
  display: block;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  margin-top: var(--space-8);
  opacity: 0.9;
}

/* Staggered Animation for Age Buttons */
.age-btn:nth-child(1) { animation: ageBtn-slideIn 0.6s var(--ease-bounce) 0.1s both; }
.age-btn:nth-child(2) { animation: ageBtn-slideIn 0.6s var(--ease-bounce) 0.3s both; }
.age-btn:nth-child(3) { animation: ageBtn-slideIn 0.6s var(--ease-bounce) 0.5s both; }

@keyframes ageBtn-slideIn {
  0% {
    opacity: 0;
    transform: translateY(50px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Revolutionary Sidebar Navigation */
.sidebar {
  position: fixed;
  left: -350px;
  top: 0;
  width: 350px;
  height: 100vh;
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%);
  backdrop-filter: blur(30px);
  transition: left var(--duration-slow) var(--ease-bounce);
  z-index: 1500;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow:
    var(--glass-shadow),
    0 0 50px rgba(30, 58, 138, 0.1);
  border-right: 1px solid var(--glass-border);
}

.sidebar.open {
  left: 0;
  animation: sidebarSlideIn 0.6s var(--ease-bounce);
}

@keyframes sidebarSlideIn {
  0% {
    left: -350px;
    opacity: 0.8;
  }
  50% {
    left: 10px;
    opacity: 0.9;
  }
  100% {
    left: 0;
    opacity: 1;
  }
}

.sidebar__header {
  padding: var(--space-24);
  border-bottom: 1px solid var(--glass-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--color-primary);
  color: white;
  position: relative;
  overflow: hidden;
}

.sidebar__header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color-gold);
  opacity: 0.1;
  animation: headerShimmer 3s ease-in-out infinite;
}

@keyframes headerShimmer {
  0%, 100% { opacity: 0.1; transform: translateX(-100%); }
  50% { opacity: 0.3; transform: translateX(100%); }
}

.sidebar__header h3 {
  color: white;
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 2;
}

.sidebar__close {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  font-size: var(--font-size-xl);
  cursor: pointer;
  padding: var(--space-8);
  border-radius: var(--radius-sm);
  transition: all var(--duration-normal) var(--ease-bounce);
  position: relative;
  z-index: 2;
}

.sidebar__close:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.1) rotate(90deg);
}

.sidebar__sections {
  padding: var(--space-16);
}

.section {
  margin-bottom: var(--space-24);
}

.section h4 {
  color: #1e3a8a;
  margin-bottom: var(--space-12);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
}

.section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.section li {
  margin-bottom: var(--space-4);
}

.section a {
  display: block;
  padding: var(--space-8) var(--space-12);
  color: var(--color-text);
  text-decoration: none;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  transition: all var(--duration-fast) var(--ease-standard);
}

.section a:hover {
  background: rgba(30, 58, 138, 0.1);
  color: #1e3a8a;
}

.section a.active {
  background: #1e3a8a;
  color: white;
}

/* Main Presentation */
.presentation {
  display: flex;
  flex-direction: column;
  height: 100vh;
  transition: margin-left var(--duration-normal) var(--ease-standard);
}

.presentation.sidebar-open {
  margin-left: 300px;
}

.presentation__nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-16) var(--space-24);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.slide-counter {
  font-weight: var(--font-weight-medium);
  color: #1e3a8a;
  font-size: var(--font-size-lg);
}

.age-indicator {
  background: #ffd700;
  color: #1e3a8a;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}

/* Revolutionary Slides System */
.slides-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateX(100%) scale(0.9);
  transition: all var(--duration-slow) var(--ease-bounce);
  background:
    radial-gradient(circle at 20% 80%, rgba(251, 191, 36, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(30, 58, 138, 0.05) 0%, transparent 50%),
    linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-lg);
  margin: var(--space-16);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

/* Slide Background Animation */
.slide::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), rgba(251, 191, 36, 0.1) 0%, transparent 30%);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-standard);
  pointer-events: none;
}

.slide:hover::before {
  opacity: 1;
}

.slide.active {
  opacity: 1;
  transform: translateX(0) scale(1);
  animation: slideEnter 0.8s var(--ease-bounce);
}

.slide.prev {
  transform: translateX(-100%) scale(0.9);
}

@keyframes slideEnter {
  0% {
    opacity: 0;
    transform: translateX(100%) scale(0.8) rotateY(45deg);
  }
  50% {
    opacity: 0.7;
    transform: translateX(0) scale(1.02) rotateY(0deg);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1) rotateY(0deg);
  }
}

.slide__content {
  max-width: 900px;
  text-align: center;
  padding: var(--space-32);
  color: var(--color-text);
  position: relative;
  z-index: 2;
  animation: contentFadeIn 1s var(--ease-standard) 0.3s both;
}

@keyframes contentFadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide__title {
  font-size: clamp(2rem, 5vw, 4rem);
  background: var(--color-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-24);
  font-weight: var(--font-weight-bold);
  text-shadow: 0 0 30px rgba(30, 58, 138, 0.3);
  animation: titleSlideIn 0.8s var(--ease-bounce) 0.5s both;
  position: relative;
}

.slide__title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: var(--color-gold);
  border-radius: 2px;
  animation: underlineExpand 0.6s var(--ease-standard) 1s both;
}

@keyframes titleSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-50px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes underlineExpand {
  0% { width: 0; }
  100% { width: 100px; }
}

.slide__subtitle {
  font-size: clamp(1.2rem, 3vw, 2rem);
  background: var(--color-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: var(--space-32);
  font-weight: var(--font-weight-semibold);
  text-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
  animation: subtitleSlideIn 0.8s var(--ease-bounce) 0.7s both;
}

@keyframes subtitleSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Spectacular Orthodox Cross */
.orthodox-cross {
  font-size: 6rem;
  background: var(--color-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: var(--space-32) 0;
  text-shadow: 0 0 50px rgba(251, 191, 36, 0.6);
  animation: crossGlow 3s ease-in-out infinite, crossFloat 4s ease-in-out infinite;
  position: relative;
  display: inline-block;
}

.orthodox-cross::before {
  content: '✠';
  position: absolute;
  top: 0;
  left: 0;
  background: var(--color-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  opacity: 0.3;
  animation: crossShadow 3s ease-in-out infinite reverse;
}

@keyframes crossGlow {
  0%, 100% {
    text-shadow: 0 0 50px rgba(251, 191, 36, 0.6);
    filter: drop-shadow(0 0 20px rgba(251, 191, 36, 0.4));
  }
  50% {
    text-shadow: 0 0 80px rgba(251, 191, 36, 0.9);
    filter: drop-shadow(0 0 40px rgba(251, 191, 36, 0.7));
  }
}

@keyframes crossFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(1deg); }
}

@keyframes crossShadow {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.1; transform: scale(1.1); }
}

/* Greeting Demo */
.greeting-demo {
  background: rgba(30, 58, 138, 0.1);
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  margin: var(--space-24) 0;
  border: 2px solid rgba(255, 215, 0, 0.3);
}

.greeting-text, .greeting-response {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin: var(--space-12) 0;
}

.greeting-text {
  color: #1e3a8a;
}

.greeting-response {
  color: #ffd700;
}

/* Timeline */
.timeline-container {
  margin: var(--space-32) 0;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: var(--space-24);
  background: rgba(255, 255, 255, 0.8);
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  border-left: 4px solid #ffd700;
}

.timeline-year {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: #1e3a8a;
  min-width: 100px;
}

.timeline-content h3 {
  color: #1e3a8a;
  margin-bottom: var(--space-8);
}

/* Founder Info */
.founder-info {
  display: flex;
  align-items: center;
  gap: var(--space-32);
  margin: var(--space-32) 0;
  background: rgba(255, 255, 255, 0.8);
  padding: var(--space-24);
  border-radius: var(--radius-lg);
}

.founder-portrait {
  font-size: 4rem;
  background: #ffd700;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.founder-details {
  flex: 1;
  text-align: left;
}

/* Age-specific content */
.age-content {
  display: none;
}

.age-content.active {
  display: block;
}

/* Simple icons for young children */
.simple-icons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-16);
  margin: var(--space-24) 0;
}

.icon {
  background: rgba(255, 215, 0, 0.2);
  padding: var(--space-16);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-lg);
}

/* Soldier qualities list */
.soldier-qualities {
  text-align: left;
  max-width: 600px;
  margin: 0 auto;
}

.soldier-qualities li {
  margin-bottom: var(--space-12);
  padding-left: var(--space-16);
  position: relative;
}

.soldier-qualities li::before {
  content: "⚔️";
  position: absolute;
  left: 0;
}

/* Revolutionary Interactive Buttons */
.interactive-btn {
  margin: var(--space-20);
  padding: var(--space-16) var(--space-32);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-lg);
  transition: all var(--duration-normal) var(--ease-bounce);
  position: relative;
  overflow: hidden;
  background: var(--color-primary);
  border: 2px solid transparent;
  box-shadow: var(--glow-primary), 0 10px 30px rgba(30, 58, 138, 0.3);
  animation: buttonPulse 2s ease-in-out infinite;
}

.interactive-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--duration-slow) var(--ease-standard);
}

.interactive-btn:hover::before {
  left: 100%;
}

.interactive-btn:hover {
  transform: translateY(-6px) scale(1.05);
  box-shadow: var(--glow-primary), 0 20px 50px rgba(30, 58, 138, 0.5);
  animation: none;
}

.interactive-btn:active {
  transform: translateY(-2px) scale(1.02);
}

@keyframes buttonPulse {
  0%, 100% {
    box-shadow: var(--glow-primary), 0 10px 30px rgba(30, 58, 138, 0.3);
  }
  50% {
    box-shadow: var(--glow-primary), 0 15px 40px rgba(30, 58, 138, 0.4);
  }
}

/* Greeting Practice */
.greeting-practice {
  background: rgba(30, 58, 138, 0.1);
  padding: var(--space-32);
  border-radius: var(--radius-lg);
  margin: var(--space-24) 0;
}

.greeting-demonstration {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-24);
  margin-bottom: var(--space-24);
}

.greeting-step {
  background: rgba(255, 255, 255, 0.8);
  padding: var(--space-16);
  border-radius: var(--radius-lg);
}

.speaker {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-8);
}

.greeting-line {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: #1e3a8a;
}

/* Final blessing */
.final-blessing {
  background: rgba(30, 58, 138, 0.1);
  padding: var(--space-32);
  border-radius: var(--radius-lg);
  border: 2px solid rgba(255, 215, 0, 0.3);
}

.blessing-text {
  font-size: var(--font-size-xl);
  font-style: italic;
  margin: var(--space-24) 0;
  color: #1e3a8a;
}

.final-greeting {
  margin: var(--space-24) 0;
}

/* Navigation Controls */
.presentation__controls {
  display: flex;
  justify-content: space-between;
  padding: var(--space-16) var(--space-24);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-btn {
  min-width: 120px;
  padding: var(--space-12) var(--space-24);
  font-weight: var(--font-weight-medium);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Spectacular Modal System */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 30% 70%, rgba(30, 58, 138, 0.8) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(251, 191, 36, 0.6) 0%, transparent 50%),
    rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-slow) var(--ease-bounce);
  backdrop-filter: blur(20px);
}

.modal.open {
  opacity: 1;
  visibility: visible;
  animation: modalBackdrop 0.6s var(--ease-bounce);
}

@keyframes modalBackdrop {
  0% {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  100% {
    opacity: 1;
    backdrop-filter: blur(20px);
  }
}

.modal__content {
  background: var(--color-surface-elevated);
  border-radius: var(--radius-lg);
  max-width: 700px;
  max-height: 85vh;
  overflow-y: auto;
  position: relative;
  box-shadow:
    var(--glass-shadow),
    0 25px 50px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-backdrop);
  transform: scale(0.7) rotateX(45deg);
  transition: transform var(--duration-slow) var(--ease-bounce);
}

.modal.open .modal__content {
  transform: scale(1) rotateX(0deg);
  animation: modalSlideIn 0.8s var(--ease-bounce);
}

@keyframes modalSlideIn {
  0% {
    opacity: 0;
    transform: scale(0.7) rotateX(45deg) translateY(50px);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05) rotateX(0deg) translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotateX(0deg) translateY(0);
  }
}

.modal__close {
  position: absolute;
  top: var(--space-12);
  right: var(--space-16);
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  cursor: pointer;
  color: var(--color-text-secondary);
  z-index: 1;
}

.modal__body {
  padding: var(--space-32);
}

/* Responsive Design */
@media (max-width: 768px) {
  .age-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .age-btn {
    width: 100%;
    max-width: 250px;
  }
  
  .sidebar {
    width: 280px;
    left: -280px;
  }
  
  .presentation.sidebar-open {
    margin-left: 280px;
  }
  
  .slide__title {
    font-size: var(--font-size-3xl);
  }
  
  .slide__content {
    padding: var(--space-16);
  }
  
  .founder-info {
    flex-direction: column;
    text-align: center;
  }
  
  .greeting-demonstration {
    grid-template-columns: 1fr;
  }
  
  .simple-icons {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .presentation__nav {
    padding: var(--space-12);
  }
  
  .slide__title {
    font-size: var(--font-size-2xl);
  }
  
  .slide__subtitle {
    font-size: var(--font-size-xl);
  }
  
  .orthodox-cross {
    font-size: 3rem;
  }
  
  .timeline-item {
    flex-direction: column;
    text-align: center;
  }
}

/* Hidden class */
.hidden {
  display: none !important;
}

/* Spectacular Progress Bar */
.progress-container {
  display: flex;
  align-items: center;
  gap: var(--space-16);
  flex: 1;
  max-width: 400px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-fill {
  height: 100%;
  background: var(--color-gold);
  border-radius: var(--radius-full);
  transition: width var(--duration-slow) var(--ease-bounce);
  width: 4.17%; /* 1/24 slides */
  position: relative;
  box-shadow:
    0 0 20px rgba(251, 191, 36, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: progressGlow 2s ease-in-out infinite;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6));
  border-radius: var(--radius-full);
  animation: progressPulse 1.5s ease-in-out infinite;
}

@keyframes progressGlow {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(251, 191, 36, 0.6),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  50% {
    box-shadow:
      0 0 30px rgba(251, 191, 36, 0.8),
      inset 0 1px 0 rgba(255, 255, 255, 0.5);
  }
}

@keyframes progressPulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Enhanced Breadcrumb Navigation */
.breadcrumb {
  display: flex;
  align-items: center;
  font-size: var(--font-size-md);
  color: var(--color-text-light);
  font-weight: var(--font-weight-semibold);
}

.breadcrumb-section {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-lg);
  transition: all var(--duration-normal) var(--ease-bounce);
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
}

.breadcrumb-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.3), transparent);
  transition: left var(--duration-slow) var(--ease-standard);
}

.breadcrumb-section:hover::before {
  left: 100%;
}

.breadcrumb-section:hover {
  transform: translateY(-2px);
  box-shadow: var(--glass-shadow), var(--glow-gold);
  border-color: var(--color-gold-solid);
}

/* Achievement System */
.achievements-container {
  position: relative;
}

.achievements-toggle {
  background: #ffd700;
  color: #1e3a8a;
  border: none;
  padding: var(--space-8) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.achievements-toggle:hover {
  background: #ffed4e;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.achievement-count {
  background: #1e3a8a;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
}

.achievements-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--space-16);
  min-width: 280px;
  max-height: 400px;
  overflow-y: auto;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--duration-normal) var(--ease-standard);
  z-index: 1000;
  border: 1px solid var(--color-border);
}

.achievements-dropdown.open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.achievements-dropdown h3 {
  margin: 0 0 var(--space-12) 0;
  color: #1e3a8a;
  font-size: var(--font-size-lg);
}

.achievements-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  padding: var(--space-8);
  border-radius: var(--radius-sm);
  transition: background var(--duration-fast) var(--ease-standard);
}

.achievement-item.earned {
  background: rgba(255, 215, 0, 0.1);
}

.achievement-item.locked {
  opacity: 0.5;
}

.achievement-icon {
  font-size: var(--font-size-xl);
  width: 32px;
  text-align: center;
}

.achievement-info {
  flex: 1;
}

.achievement-title {
  font-weight: var(--font-weight-semibold);
  color: #1e3a8a;
  margin: 0;
  font-size: var(--font-size-sm);
}

.achievement-description {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
}

/* Revolutionary Notification System */
.notification-container {
  position: fixed;
  top: var(--space-24);
  right: var(--space-24);
  z-index: 3000;
  display: flex;
  flex-direction: column;
  gap: var(--space-12);
  max-width: 380px;
}

.notification {
  background: var(--color-surface-elevated);
  border-radius: var(--radius-lg);
  padding: var(--space-20);
  box-shadow:
    var(--glass-shadow),
    0 20px 40px rgba(0, 0, 0, 0.2);
  border: 1px solid var(--glass-border);
  border-left: 4px solid var(--color-gold-solid);
  backdrop-filter: var(--glass-backdrop);
  transform: translateX(120%) scale(0.8);
  opacity: 0;
  transition: all var(--duration-slow) var(--ease-bounce);
  display: flex;
  align-items: flex-start;
  gap: var(--space-16);
  position: relative;
  overflow: hidden;
}

.notification::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--duration-slow) var(--ease-standard);
}

.notification.show {
  transform: translateX(0) scale(1);
  opacity: 1;
  animation: notificationSlideIn 0.6s var(--ease-bounce);
}

.notification.show::before {
  left: 100%;
}

@keyframes notificationSlideIn {
  0% {
    transform: translateX(120%) scale(0.8) rotateY(45deg);
    opacity: 0;
  }
  50% {
    transform: translateX(-10px) scale(1.02) rotateY(0deg);
    opacity: 0.9;
  }
  100% {
    transform: translateX(0) scale(1) rotateY(0deg);
    opacity: 1;
  }
}

.notification.success {
  border-left-color: var(--color-success);
}

.notification.error {
  border-left-color: var(--color-error);
}

.notification.info {
  border-left-color: var(--color-info);
}

.notification-icon {
  font-size: var(--font-size-lg);
  margin-top: var(--space-2);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: var(--font-weight-semibold);
  color: #1e3a8a;
  margin: 0 0 var(--space-4) 0;
  font-size: var(--font-size-sm);
}

.notification-message {
  font-size: var(--font-size-sm);
  color: var(--color-text);
  margin: 0;
}

.notification-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-sm);
  transition: color var(--duration-fast) var(--ease-standard);
}

.notification-close:hover {
  color: var(--color-text);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(30, 58, 138, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-normal) var(--ease-standard);
}

.loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  text-align: center;
  color: white;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #ffd700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-16) auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Revolutionary Micro-interactions */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(30, 58, 138, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(30, 58, 138, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(30, 58, 138, 0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0) scale(1);
  }
  40%, 43% {
    transform: translate3d(0, -12px, 0) scale(1.02);
  }
  70% {
    transform: translate3d(0, -6px, 0) scale(1.01);
  }
  90% {
    transform: translate3d(0, -3px, 0) scale(1.005);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0) rotate(0deg);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-3px) rotate(-1deg);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(3px) rotate(1deg);
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.1);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.1);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes glow {
  0%, 100% {
    text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
  }
  50% {
    text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes zoomIn {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes rotateIn {
  0% {
    transform: rotate(-180deg) scale(0.8);
    opacity: 0;
  }
  100% {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
}

.interactive-btn:focus {
  animation: pulse 1s infinite;
}

.interactive-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

.interactive-btn.success {
  animation: bounce 0.6s ease-in-out;
}

.interactive-btn.error {
  animation: shake 0.5s ease-in-out;
}

/* Enhanced Focus Management */
.focus-trap {
  outline: 2px solid #ffd700;
  outline-offset: 2px;
}

/* Skip Link for Accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #1e3a8a;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 4000;
  transition: top var(--duration-fast) var(--ease-standard);
}

.skip-link:focus {
  top: 6px;
}

/* Touch-friendly improvements for mobile */
@media (max-width: 768px) {
  .interactive-btn {
    min-height: 44px;
    min-width: 44px;
    padding: var(--space-12) var(--space-16);
  }

  .achievements-toggle {
    min-height: 44px;
    padding: var(--space-12) var(--space-16);
  }

  .nav-btn {
    min-height: 44px;
    padding: var(--space-16) var(--space-20);
  }

  .notification-container {
    left: var(--space-12);
    right: var(--space-12);
    max-width: none;
  }

  .achievements-dropdown {
    left: 0;
    right: 0;
    min-width: auto;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .progress-fill {
    background: #000;
  }

  .achievement-count {
    background: #000;
    color: #fff;
  }

  .notification {
    border-width: 2px;
    border-style: solid;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .interactive-btn:hover {
    transform: none;
  }

  .notification {
    transition: opacity var(--duration-fast) ease;
  }

  .spinner {
    animation: none;
  }

  .interactive-btn:focus {
    animation: none;
    outline: 2px solid #ffd700;
  }
}