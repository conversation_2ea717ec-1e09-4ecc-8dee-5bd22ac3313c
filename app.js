class OastePresentationApp {
    constructor() {
        this.currentSlide = 0;
        this.totalSlides = 24;
        this.selectedAge = null;
        this.slides = [];
        this.isPlaying = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.createAllSlides();
        this.showAgeSelector();
    }
    
    setupEventListeners() {
        // Age selection
        document.querySelectorAll('.age-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectAge(e.target.dataset.age);
            });
        });
        
        // Navigation
        document.getElementById('prevBtn').addEventListener('click', () => this.previousSlide());
        document.getElementById('nextBtn').addEventListener('click', () => this.nextSlide());
        
        // Sidebar
        document.getElementById('menuToggle').addEventListener('click', () => this.toggleSidebar());
        document.getElementById('sidebarClose').addEventListener('click', () => this.closeSidebar());
        
        // Sidebar navigation
        document.querySelectorAll('.sidebar a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const slideIndex = parseInt(e.target.dataset.slide);
                this.goToSlide(slideIndex);
                this.closeSidebar();
            });
        });
        
        // Interactive buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('interactive-btn')) {
                this.handleInteractiveAction(e.target.dataset.action);
            }
        });
        
        // Modal
        document.getElementById('modalClose').addEventListener('click', () => this.closeModal());
        document.getElementById('interactiveModal').addEventListener('click', (e) => {
            if (e.target.id === 'interactiveModal') {
                this.closeModal();
            }
        });
        
        // Restart presentation
        document.getElementById('restartPresentation').addEventListener('click', () => {
            this.restartPresentation();
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') this.previousSlide();
            if (e.key === 'ArrowRight') this.nextSlide();
            if (e.key === 'Escape') this.closeModal();
        });
    }
    
    createAllSlides() {
        const slidesContainer = document.getElementById('slidesContainer');
        
        // Define all slide content
        const slideContents = [
            // Slides 0-4 already exist in HTML, so we'll add the missing ones
            
            // Slide 5: Simbolurile noastre
            {
                title: "Simbolurile noastre sfinte",
                content: `
                    <div class="symbols-grid">
                        <div class="symbol-item">
                            <div class="symbol">✠</div>
                            <h3>Crucea Ortodoxă</h3>
                            <p class="age-content" data-age="8-10">Semnul dragostei lui Iisus pentru noi!</p>
                            <p class="age-content" data-age="11-14">Simbolul jertfei și învierii Mântuitorului nostru.</p>
                            <p class="age-content" data-age="15-18">Expresia centrală a credinței ortodoxe și a mărturisirii jertfei lui Hristos.</p>
                        </div>
                        <div class="symbol-item">
                            <div class="symbol">🏛️</div>
                            <h3>Biserica</h3>
                            <p class="age-content" data-age="8-10">Casa noastră duhovnicească!</p>
                            <p class="age-content" data-age="11-14">Locul unde ne întâlnim cu Dumnezeu și cu credincioșii.</p>
                            <p class="age-content" data-age="15-18">Comunitatea euharistică și trupul mistic al lui Hristos.</p>
                        </div>
                    </div>
                    <button class="btn btn--primary interactive-btn" data-action="symbolsQuiz">🎯 Quiz despre simboluri</button>
                `
            },
            
            // Slide 6: Biserica noastră
            {
                title: "Biserica noastră - Casa lui Dumnezeu",
                content: `
                    <div class="church-info">
                        <div class="church-visual">⛪</div>
                        <div class="church-description">
                            <p class="age-content" data-age="8-10">Biserica este casa lui Dumnezeu unde mergem să ne rugăm și să cântăm!</p>
                            <p class="age-content" data-age="11-14">În biserica ortodoxă ne întâlnim cu Dumnezeu prin rugăciune, cântare și Sfintele Taine.</p>
                            <p class="age-content" data-age="15-18">Biserica Ortodoxă Română este păstrătoarea tradiției apostolice și a adevăratei credinți creștine.</p>
                        </div>
                    </div>
                    <div class="church-elements">
                        <div class="element">🕯️ Lumânări</div>
                        <div class="element">📖 Evanghelia</div>
                        <div class="element">🍞 Euharistia</div>
                    </div>
                    <button class="btn btn--secondary interactive-btn" data-action="churchTour">🏛️ Turul bisericii</button>
                `
            }
        ];
        
        // Add the missing slides to the container
        slideContents.forEach((slide, index) => {
            const slideIndex = index + 5; // Starting from slide 5
            if (slideIndex < this.totalSlides && !document.querySelector(`[data-slide="${slideIndex}"]`)) {
                const slideElement = this.createSlideElement(slideIndex, slide.title, slide.content);
                slidesContainer.appendChild(slideElement);
            }
        });
        
        // Create remaining slides programmatically for demonstration
        this.createRemainingSlides();
    }
    
    createSlideElement(index, title, content) {
        const slide = document.createElement('div');
        slide.className = 'slide';
        slide.setAttribute('data-slide', index);
        
        slide.innerHTML = `
            <div class="slide__content">
                <h1 class="slide__title">${title}</h1>
                ${content}
            </div>
        `;
        
        return slide;
    }
    
    createRemainingSlides() {
        const slidesContainer = document.getElementById('slidesContainer');
        const remainingSlides = [
            {
                title: "Familia Oastei Domnului",
                content: `
                    <div class="family-circle">
                        <div class="family-member">👨‍👩‍👧‍👦 Familii</div>
                        <div class="family-member">👥 Tineri</div>
                        <div class="family-member">👶 Copii</div>
                        <div class="family-member">👴👵 Bătrâni</div>
                    </div>
                    <p class="age-content" data-age="8-10">Suntem o familie mare și frumoasă!</p>
                    <p class="age-content" data-age="11-14">Comunitatea noastră este formată din oameni de toate vârstele unidos în credință.</p>
                    <p class="age-content" data-age="15-18">Oastea Domnului este o comunitate intergenerațională bazată pe solidaritatea creștină.</p>
                    <button class="btn btn--primary interactive-btn" data-action="familyStories">📚 Povești din familie</button>
                `
            },
            // Add more slides as needed...
        ];
        
        remainingSlides.forEach((slide, index) => {
            const slideIndex = index + 7; // Continue from where we left off
            if (slideIndex < this.totalSlides && !document.querySelector(`[data-slide="${slideIndex}"]`)) {
                const slideElement = this.createSlideElement(slideIndex, slide.title, slide.content);
                slidesContainer.appendChild(slideElement);
            }
        });
    }
    
    selectAge(age) {
        this.selectedAge = age;
        document.getElementById('ageSelector').classList.add('hidden');
        document.getElementById('ageIndicator').textContent = this.getAgeGroupName(age);
        
        // Show age-appropriate content
        this.updateContentForAge();
        
        // Start presentation
        this.goToSlide(0);
    }
    
    getAgeGroupName(age) {
        const ageGroups = {
            '8-10': 'Micii Ostași',
            '11-14': 'Ostașii în Creștere',
            '15-18': 'Tinerii Ostași'
        };
        return ageGroups[age] || age;
    }
    
    updateContentForAge() {
        // Hide all age-specific content
        document.querySelectorAll('.age-content').forEach(el => {
            el.classList.remove('active');
        });
        
        // Show content for selected age
        document.querySelectorAll(`[data-age="${this.selectedAge}"]`).forEach(el => {
            el.classList.add('active');
        });
    }
    
    showAgeSelector() {
        document.getElementById('ageSelector').classList.remove('hidden');
    }
    
    goToSlide(index) {
        if (index < 0 || index >= this.totalSlides) return;
        
        // Update current slide
        document.querySelectorAll('.slide').forEach((slide, i) => {
            slide.classList.remove('active', 'prev');
            if (i === index) {
                slide.classList.add('active');
            } else if (i < index) {
                slide.classList.add('prev');
            }
        });
        
        this.currentSlide = index;
        
        // Update counter
        document.getElementById('currentSlide').textContent = index + 1;
        
        // Update navigation buttons
        document.getElementById('prevBtn').disabled = index === 0;
        document.getElementById('nextBtn').disabled = index === this.totalSlides - 1;
        
        // Update sidebar active state
        document.querySelectorAll('.sidebar a').forEach(link => {
            link.classList.remove('active');
            if (parseInt(link.dataset.slide) === index) {
                link.classList.add('active');
            }
        });
        
        // Update age-appropriate content for current slide
        this.updateContentForAge();
    }
    
    nextSlide() {
        if (this.currentSlide < this.totalSlides - 1) {
            this.goToSlide(this.currentSlide + 1);
        }
    }
    
    previousSlide() {
        if (this.currentSlide > 0) {
            this.goToSlide(this.currentSlide - 1);
        }
    }
    
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const presentation = document.getElementById('presentation');
        
        sidebar.classList.toggle('open');
        presentation.classList.toggle('sidebar-open');
    }
    
    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const presentation = document.getElementById('presentation');
        
        sidebar.classList.remove('open');
        presentation.classList.remove('sidebar-open');
    }
    
    handleInteractiveAction(action) {
        switch (action) {
            case 'playGreeting':
                this.playGreeting();
                break;
            case 'showHistory':
                this.showHistory();
                break;
            case 'bioTimeline':
                this.showBioTimeline();
                break;
            case 'practiceGreeting':
                this.practiceGreeting();
                break;
            case 'symbolsQuiz':
                this.showSymbolsQuiz();
                break;
            case 'churchTour':
                this.showChurchTour();
                break;
            case 'familyStories':
                this.showFamilyStories();
                break;
            case 'finalPrayer':
                this.showFinalPrayer();
                break;
            default:
                this.showGenericActivity(action);
        }
    }
    
    playGreeting() {
        this.showModal(`
            <h2>🔊 Salutul Oastei Domnului</h2>
            <div class="greeting-practice">
                <div class="greeting-step">
                    <p><strong>Tu spui:</strong></p>
                    <p class="greeting-line">"Slăvit să fie Domnul!"</p>
                    <button class="btn btn--primary" onclick="this.innerHTML='🔊 Se redă...'">▶️ Ascultă</button>
                </div>
                <div class="greeting-step">
                    <p><strong>Ceilalți răspund:</strong></p>
                    <p class="greeting-line">"În veci, amin!"</p>
                    <button class="btn btn--primary" onclick="this.innerHTML='🔊 Se redă...'">▶️ Ascultă</button>
                </div>
            </div>
            <p class="text-center">
                <em>Acest salut ne amintește că Dumnezeu este în centrul vieții noastre!</em>
            </p>
        `);
    }
    
    showHistory() {
        const ageContent = {
            '8-10': 'În 1923, un preot foarte bun pe nume Iosif Trifa a început Oastea Domnului pentru a-i ajuta pe oameni să îl cunoască mai bine pe Iisus!',
            '11-14': 'Oastea Domnului a fost fondată în 1923 de Părintele Iosif Trifa în Transilvania, ca o mișcare de trezire spirituală în Biserica Ortodoxă Română.',
            '15-18': 'Mișcarea Oastea Domnului, fondată în 1923 de Arhimandritul Iosif Trifa, a reprezentat o încercare de reformă și trezire spirituală în cadrul Bisericii Ortodoxe Române, promovând o spiritualitate personală și comunitară mai vie.'
        };
        
        this.showModal(`
            <h2>📚 Istoria Oastei Domnului</h2>
            <div class="timeline-container">
                <div class="timeline-item">
                    <div class="timeline-year">1923</div>
                    <div class="timeline-content">
                        <h3>Înființarea</h3>
                        <p>${ageContent[this.selectedAge]}</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">1925</div>
                    <div class="timeline-content">
                        <h3>Prima publicație</h3>
                        <p>Apare revista "Iisus Biruitorul"</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-year">Prezent</div>
                    <div class="timeline-content">
                        <h3>Continuarea misiunii</h3>
                        <p>Mișcarea continuă să inspire credincioșii ortodocși</p>
                    </div>
                </div>
            </div>
        `);
    }
    
    showBioTimeline() {
        this.showModal(`
            <h2>⏰ Viața Părintelui Iosif Trifa</h2>
            <div class="biography-timeline">
                <div class="bio-event">
                    <strong>1888</strong> - Nașterea în Bucium, Alba
                </div>
                <div class="bio-event">
                    <strong>1910</strong> - Hirotonirea ca preot
                </div>
                <div class="bio-event">
                    <strong>1923</strong> - Fondarea Oastei Domnului
                </div>
                <div class="bio-event">
                    <strong>1925</strong> - Începutul publicației "Iisus Biruitorul"
                </div>
                <div class="bio-event">
                    <strong>1938</strong> - Trecerea la cele veșnice
                </div>
            </div>
            <p class="text-center">
                <em>Un om de Dumnezeu care a dedicat viața treziei spirituale!</em>
            </p>
        `);
    }
    
    practiceGreeting() {
        let step = 0;
        const steps = [
            'Ridică mâna dreaptă și spune: "Slăvit să fie Domnul!"',
            'Ascultă răspunsul: "În veci, amin!"',
            'Felicitări! Ai învățat salutul!'
        ];
        
        this.showModal(`
            <h2>🎯 Exersează salutul</h2>
            <div class="practice-container">
                <div class="practice-step" id="practiceStep">
                    <p>${steps[0]}</p>
                    <button class="btn btn--primary" onclick="window.app.nextPracticeStep()">Următorul pas</button>
                </div>
            </div>
        `);
        
        this.practiceStep = 0;
        this.practiceSteps = steps;
    }
    
    nextPracticeStep() {
        this.practiceStep++;
        if (this.practiceStep < this.practiceSteps.length) {
            document.getElementById('practiceStep').innerHTML = `
                <p>${this.practiceSteps[this.practiceStep]}</p>
                <button class="btn btn--primary" onclick="window.app.nextPracticeStep()">
                    ${this.practiceStep === this.practiceSteps.length - 1 ? 'Termină' : 'Următorul pas'}
                </button>
            `;
        } else {
            this.closeModal();
        }
    }
    
    showSymbolsQuiz() {
        this.showModal(`
            <h2>🎯 Quiz despre simboluri</h2>
            <div class="quiz-container">
                <div class="quiz-question">
                    <p>Ce simbolizează crucea ortodoxă?</p>
                    <div class="quiz-options">
                        <button class="btn btn--outline quiz-option" onclick="window.app.answerQuiz(true)">
                            Dragostea și jertfa lui Iisus
                        </button>
                        <button class="btn btn--outline quiz-option" onclick="window.app.answerQuiz(false)">
                            Doar o decorație
                        </button>
                    </div>
                </div>
            </div>
        `);
    }
    
    answerQuiz(correct) {
        const modal = document.getElementById('modalBody');
        if (correct) {
            modal.innerHTML = `
                <h2>🎉 Corect!</h2>
                <p>Crucea ortodoxă simbolizează dragostea infinită și jertfa lui Iisus pentru noi!</p>
                <button class="btn btn--primary" onclick="window.app.closeModal()">Continuă</button>
            `;
        } else {
            modal.innerHTML = `
                <h2>😊 Încearcă din nou!</h2>
                <p>Gândește-te la ce a făcut Iisus pentru noi pe cruce...</p>
                <button class="btn btn--secondary" onclick="window.app.showSymbolsQuiz()">Încearcă din nou</button>
            `;
        }
    }
    
    showChurchTour() {
        this.showModal(`
            <h2>🏛️ Turul bisericii ortodoxe</h2>
            <div class="church-tour">
                <div class="tour-section">
                    <h3>⛪ Nartexul</h3>
                    <p>Locul unde ne pregătim pentru rugăciune</p>
                </div>
                <div class="tour-section">
                    <h3>🕯️ Naosul</h3>
                    <p>Aici stau credincioșii în timpul slujbei</p>
                </div>
                <div class="tour-section">
                    <h3>✨ Altarul</h3>
                    <p>Locul cel mai sfânt, unde se oficiază Euharistia</p>
                </div>
            </div>
            <p class="text-center">
                <em>Fiecare parte a bisericii are o însemnătate spirituală specială!</em>
            </p>
        `);
    }
    
    showFamilyStories() {
        this.showModal(`
            <h2>📚 Povești din familia Oastei Domnului</h2>
            <div class="family-stories">
                <div class="story">
                    <h3>👨‍👩‍👧‍👦 Familia Popescu</h3>
                    <p>"Prin Oastea Domnului am învățat să ne rugăm împreună în fiecare seară."</p>
                </div>
                <div class="story">
                    <h3>👥 Grupul de tineri</h3>
                    <p>"Ne întâlnim săptămânal pentru a studia Biblia și a cânta cântece duhovnicești."</p>
                </div>
                <div class="story">
                    <h3>👶 Copiii din Oaste</h3>
                    <p>"Învățăm despre Iisus prin jocuri și activități frumoase!"</p>
                </div>
            </div>
        `);
    }
    
    showFinalPrayer() {
        this.showModal(`
            <h2>🙏 Rugăciune finală</h2>
            <div class="final-prayer">
                <div class="orthodox-cross">✠</div>
                <div class="prayer-text">
                    <p><em>"Doamne Iisuse Hristoste, Fiul lui Dumnezeu, miluiește-ne pe noi păcătoșii și ajută-ne să fim ostași buni în Oastea Ta sfântă."</em></p>
                </div>
                <div class="prayer-response">
                    <p><strong>"Slăvit să fie Domnul!"</strong></p>
                    <p><strong>"În veci, amin!"</strong></p>
                </div>
                <button class="btn btn--primary" onclick="this.innerHTML='🙏 Se rostește rugăciunea...'">
                    🔊 Rostește rugăciunea
                </button>
            </div>
        `);
    }
    
    showGenericActivity(action) {
        this.showModal(`
            <h2>📱 Activitate interactivă</h2>
            <p>Această activitate (${action}) va fi dezvoltată în versiunea completă.</p>
            <p>Mulțumim pentru răbdare!</p>
        `);
    }
    
    showModal(content) {
        document.getElementById('modalBody').innerHTML = content;
        document.getElementById('interactiveModal').classList.add('open');
    }
    
    closeModal() {
        document.getElementById('interactiveModal').classList.remove('open');
    }
    
    restartPresentation() {
        this.currentSlide = 0;
        this.selectedAge = null;
        this.closeModal();
        this.closeSidebar();
        this.showAgeSelector();
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new OastePresentationApp();
});

// Add some additional CSS dynamically for better styling
const additionalStyles = `
    .symbols-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-24);
        margin: var(--space-24) 0;
    }
    
    .symbol-item {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-24);
        border-radius: var(--radius-lg);
        text-align: center;
        border: 2px solid rgba(255, 215, 0, 0.3);
    }
    
    .symbol {
        font-size: 3rem;
        color: #ffd700;
        margin-bottom: var(--space-16);
    }
    
    .church-visual {
        font-size: 4rem;
        margin: var(--space-24) 0;
    }
    
    .church-elements {
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;
        gap: var(--space-16);
        margin: var(--space-24) 0;
    }
    
    .element {
        background: rgba(255, 215, 0, 0.2);
        padding: var(--space-12) var(--space-16);
        border-radius: var(--radius-lg);
        font-size: var(--font-size-lg);
    }
    
    .family-circle {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-16);
        margin: var(--space-24) 0;
    }
    
    .family-member {
        background: rgba(255, 215, 0, 0.2);
        padding: var(--space-16);
        border-radius: var(--radius-lg);
        text-align: center;
        font-size: var(--font-size-lg);
    }
    
    .quiz-container {
        text-align: center;
    }
    
    .quiz-options {
        display: flex;
        flex-direction: column;
        gap: var(--space-12);
        margin-top: var(--space-16);
    }
    
    .quiz-option {
        padding: var(--space-12) var(--space-16);
    }
    
    .biography-timeline {
        margin: var(--space-24) 0;
    }
    
    .bio-event {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-12) var(--space-16);
        margin-bottom: var(--space-8);
        border-radius: var(--radius-sm);
        border-left: 4px solid #ffd700;
    }
    
    .church-tour {
        margin: var(--space-24) 0;
    }
    
    .tour-section {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        margin-bottom: var(--space-12);
        border-radius: var(--radius-lg);
    }
    
    .family-stories {
        margin: var(--space-24) 0;
    }
    
    .story {
        background: rgba(255, 255, 255, 0.8);
        padding: var(--space-16);
        margin-bottom: var(--space-12);
        border-radius: var(--radius-lg);
        border-left: 4px solid #1e3a8a;
    }
    
    .final-prayer {
        text-align: center;
    }
    
    .prayer-text {
        background: rgba(30, 58, 138, 0.1);
        padding: var(--space-24);
        border-radius: var(--radius-lg);
        margin: var(--space-24) 0;
        font-size: var(--font-size-lg);
    }
    
    .prayer-response {
        margin: var(--space-24) 0;
    }
    
    .prayer-response p {
        color: #1e3a8a;
        font-size: var(--font-size-xl);
        margin: var(--space-8) 0;
    }
    
    .practice-container {
        text-align: center;
        padding: var(--space-24);
    }
    
    .practice-step p {
        font-size: var(--font-size-lg);
        margin-bottom: var(--space-16);
        color: #1e3a8a;
    }
`;

// Add the additional styles
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);