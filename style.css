
:root {
  /* Colors */
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);
  --color-select-caret: rgba(19, 52, 59, 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: rgba(31, 33, 33, 1);
    --color-surface: rgba(38, 40, 40, 1);
    --color-text: rgba(245, 245, 245, 1);
    --color-text-secondary: rgba(167, 169, 169, 0.7);
    --color-primary: rgba(50, 184, 198, 1);
    --color-primary-hover: rgba(45, 166, 178, 1);
    --color-primary-active: rgba(41, 150, 161, 1);
    --color-secondary: rgba(119, 124, 124, 0.15);
    --color-secondary-hover: rgba(119, 124, 124, 0.25);
    --color-secondary-active: rgba(119, 124, 124, 0.3);
    --color-border: rgba(119, 124, 124, 0.3);
    --color-error: rgba(255, 84, 89, 1);
    --color-success: rgba(50, 184, 198, 1);
    --color-warning: rgba(230, 129, 97, 1);
    --color-info: rgba(167, 169, 169, 1);
    --color-focus-ring: rgba(50, 184, 198, 0.4);
    --color-btn-primary-text: rgba(19, 52, 59, 1);
    --color-card-border: rgba(119, 124, 124, 0.2);
    --color-card-border-inner: rgba(119, 124, 124, 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(119, 124, 124, 0.2);
    --color-border-secondary: rgba(119, 124, 124, 0.2);
    --color-select-caret: rgba(245, 245, 245, 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: 50, 184, 198;
    --color-error-rgb: 255, 84, 89;
    --color-warning-rgb: 230, 129, 97;
    --color-info-rgb: 167, 169, 169;
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  --color-background: rgba(31, 33, 33, 1);
  --color-surface: rgba(38, 40, 40, 1);
  --color-text: rgba(245, 245, 245, 1);
  --color-text-secondary: rgba(167, 169, 169, 0.7);
  --color-primary: rgba(50, 184, 198, 1);
  --color-primary-hover: rgba(45, 166, 178, 1);
  --color-primary-active: rgba(41, 150, 161, 1);
  --color-secondary: rgba(119, 124, 124, 0.15);
  --color-secondary-hover: rgba(119, 124, 124, 0.25);
  --color-secondary-active: rgba(119, 124, 124, 0.3);
  --color-border: rgba(119, 124, 124, 0.3);
  --color-error: rgba(255, 84, 89, 1);
  --color-success: rgba(50, 184, 198, 1);
  --color-warning: rgba(230, 129, 97, 1);
  --color-info: rgba(167, 169, 169, 1);
  --color-focus-ring: rgba(50, 184, 198, 0.4);
  --color-btn-primary-text: rgba(19, 52, 59, 1);
  --color-card-border: rgba(119, 124, 124, 0.15);
  --color-card-border-inner: rgba(119, 124, 124, 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(119, 124, 124, 0.2);
  --color-select-caret: rgba(245, 245, 245, 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: 50, 184, 198;
  --color-error-rgb: 255, 84, 89;
  --color-warning-rgb: 230, 129, 97;
  --color-info-rgb: 167, 169, 169;
}

[data-color-scheme="light"] {
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* Orthodox presentation specific styles */
.presentation-container {
  font-family: var(--font-family-base);
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
  position: relative;
}

/* Age Selector */
.age-selector {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(30, 58, 138, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.age-selector__content {
  text-align: center;
  color: white;
  max-width: 600px;
  padding: var(--space-32);
}

.age-selector__content h2 {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-32);
  color: #ffd700;
}

.age-buttons {
  display: flex;
  gap: var(--space-24);
  justify-content: center;
  flex-wrap: wrap;
}

.age-btn {
  padding: var(--space-24) var(--space-32);
  font-size: var(--font-size-lg);
  background: #ffd700;
  color: #1e3a8a;
  border: none;
  border-radius: var(--radius-lg);
  min-width: 160px;
  transition: all var(--duration-normal) var(--ease-standard);
  font-weight: var(--font-weight-bold);
}

.age-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
  background: #ffed4e;
}

.age-btn small {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  margin-top: var(--space-4);
}

/* Sidebar Navigation */
.sidebar {
  position: fixed;
  left: -300px;
  top: 0;
  width: 300px;
  height: 100vh;
  background: var(--color-surface);
  box-shadow: var(--shadow-lg);
  transition: left var(--duration-normal) var(--ease-standard);
  z-index: 100;
  overflow-y: auto;
}

.sidebar.open {
  left: 0;
}

.sidebar__header {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #1e3a8a;
  color: white;
}

.sidebar__header h3 {
  color: white;
  margin: 0;
}

.sidebar__close {
  background: none;
  border: none;
  color: white;
  font-size: var(--font-size-2xl);
  cursor: pointer;
  padding: var(--space-4);
}

.sidebar__sections {
  padding: var(--space-16);
}

.section {
  margin-bottom: var(--space-24);
}

.section h4 {
  color: #1e3a8a;
  margin-bottom: var(--space-12);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
}

.section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.section li {
  margin-bottom: var(--space-4);
}

.section a {
  display: block;
  padding: var(--space-8) var(--space-12);
  color: var(--color-text);
  text-decoration: none;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  transition: all var(--duration-fast) var(--ease-standard);
}

.section a:hover {
  background: rgba(30, 58, 138, 0.1);
  color: #1e3a8a;
}

.section a.active {
  background: #1e3a8a;
  color: white;
}

/* Main Presentation */
.presentation {
  display: flex;
  flex-direction: column;
  height: 100vh;
  transition: margin-left var(--duration-normal) var(--ease-standard);
}

.presentation.sidebar-open {
  margin-left: 300px;
}

.presentation__nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-16) var(--space-24);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.slide-counter {
  font-weight: var(--font-weight-medium);
  color: #1e3a8a;
  font-size: var(--font-size-lg);
}

.age-indicator {
  background: #ffd700;
  color: #1e3a8a;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
}

/* Slides */
.slides-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateX(100%);
  transition: all var(--duration-normal) var(--ease-standard);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(10px);
}

.slide.active {
  opacity: 1;
  transform: translateX(0);
}

.slide.prev {
  transform: translateX(-100%);
}

.slide__content {
  max-width: 800px;
  text-align: center;
  padding: var(--space-32);
  color: var(--color-text);
}

.slide__title {
  font-size: var(--font-size-4xl);
  color: #1e3a8a;
  margin-bottom: var(--space-16);
  font-weight: var(--font-weight-bold);
}

.slide__subtitle {
  font-size: var(--font-size-2xl);
  color: #ffd700;
  margin-bottom: var(--space-32);
  font-weight: var(--font-weight-semibold);
}

/* Orthodox Cross */
.orthodox-cross {
  font-size: 4rem;
  color: #ffd700;
  margin: var(--space-24) 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

/* Greeting Demo */
.greeting-demo {
  background: rgba(30, 58, 138, 0.1);
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  margin: var(--space-24) 0;
  border: 2px solid rgba(255, 215, 0, 0.3);
}

.greeting-text, .greeting-response {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin: var(--space-12) 0;
}

.greeting-text {
  color: #1e3a8a;
}

.greeting-response {
  color: #ffd700;
}

/* Timeline */
.timeline-container {
  margin: var(--space-32) 0;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: var(--space-24);
  background: rgba(255, 255, 255, 0.8);
  padding: var(--space-24);
  border-radius: var(--radius-lg);
  border-left: 4px solid #ffd700;
}

.timeline-year {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: #1e3a8a;
  min-width: 100px;
}

.timeline-content h3 {
  color: #1e3a8a;
  margin-bottom: var(--space-8);
}

/* Founder Info */
.founder-info {
  display: flex;
  align-items: center;
  gap: var(--space-32);
  margin: var(--space-32) 0;
  background: rgba(255, 255, 255, 0.8);
  padding: var(--space-24);
  border-radius: var(--radius-lg);
}

.founder-portrait {
  font-size: 4rem;
  background: #ffd700;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.founder-details {
  flex: 1;
  text-align: left;
}

/* Age-specific content */
.age-content {
  display: none;
}

.age-content.active {
  display: block;
}

/* Simple icons for young children */
.simple-icons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-16);
  margin: var(--space-24) 0;
}

.icon {
  background: rgba(255, 215, 0, 0.2);
  padding: var(--space-16);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-lg);
}

/* Soldier qualities list */
.soldier-qualities {
  text-align: left;
  max-width: 600px;
  margin: 0 auto;
}

.soldier-qualities li {
  margin-bottom: var(--space-12);
  padding-left: var(--space-16);
  position: relative;
}

.soldier-qualities li::before {
  content: "⚔️";
  position: absolute;
  left: 0;
}

/* Interactive buttons */
.interactive-btn {
  margin: var(--space-16);
  padding: var(--space-12) var(--space-24);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-lg);
  transition: all var(--duration-normal) var(--ease-standard);
}

.interactive-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

/* Greeting Practice */
.greeting-practice {
  background: rgba(30, 58, 138, 0.1);
  padding: var(--space-32);
  border-radius: var(--radius-lg);
  margin: var(--space-24) 0;
}

.greeting-demonstration {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-24);
  margin-bottom: var(--space-24);
}

.greeting-step {
  background: rgba(255, 255, 255, 0.8);
  padding: var(--space-16);
  border-radius: var(--radius-lg);
}

.speaker {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-8);
}

.greeting-line {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: #1e3a8a;
}

/* Final blessing */
.final-blessing {
  background: rgba(30, 58, 138, 0.1);
  padding: var(--space-32);
  border-radius: var(--radius-lg);
  border: 2px solid rgba(255, 215, 0, 0.3);
}

.blessing-text {
  font-size: var(--font-size-xl);
  font-style: italic;
  margin: var(--space-24) 0;
  color: #1e3a8a;
}

.final-greeting {
  margin: var(--space-24) 0;
}

/* Navigation Controls */
.presentation__controls {
  display: flex;
  justify-content: space-between;
  padding: var(--space-16) var(--space-24);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-btn {
  min-width: 120px;
  padding: var(--space-12) var(--space-24);
  font-weight: var(--font-weight-medium);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-normal) var(--ease-standard);
}

.modal.open {
  opacity: 1;
  visibility: visible;
}

.modal__content {
  background: white;
  border-radius: var(--radius-lg);
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  transform: scale(0.8);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.modal.open .modal__content {
  transform: scale(1);
}

.modal__close {
  position: absolute;
  top: var(--space-12);
  right: var(--space-16);
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  cursor: pointer;
  color: var(--color-text-secondary);
  z-index: 1;
}

.modal__body {
  padding: var(--space-32);
}

/* Responsive Design */
@media (max-width: 768px) {
  .age-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .age-btn {
    width: 100%;
    max-width: 250px;
  }
  
  .sidebar {
    width: 280px;
    left: -280px;
  }
  
  .presentation.sidebar-open {
    margin-left: 280px;
  }
  
  .slide__title {
    font-size: var(--font-size-3xl);
  }
  
  .slide__content {
    padding: var(--space-16);
  }
  
  .founder-info {
    flex-direction: column;
    text-align: center;
  }
  
  .greeting-demonstration {
    grid-template-columns: 1fr;
  }
  
  .simple-icons {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .presentation__nav {
    padding: var(--space-12);
  }
  
  .slide__title {
    font-size: var(--font-size-2xl);
  }
  
  .slide__subtitle {
    font-size: var(--font-size-xl);
  }
  
  .orthodox-cross {
    font-size: 3rem;
  }
  
  .timeline-item {
    flex-direction: column;
    text-align: center;
  }
}

/* Hidden class */
.hidden {
  display: none !important;
}

/* Enhanced Progress Bar */
.progress-container {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  flex: 1;
  max-width: 300px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ffd700, #ffed4e);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-standard);
  width: 4.17%; /* 1/24 slides */
}

/* Breadcrumb Navigation */
.breadcrumb {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: #1e3a8a;
  font-weight: var(--font-weight-medium);
}

.breadcrumb-section {
  background: rgba(255, 215, 0, 0.2);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-sm);
  transition: all var(--duration-fast) var(--ease-standard);
}

/* Achievement System */
.achievements-container {
  position: relative;
}

.achievements-toggle {
  background: #ffd700;
  color: #1e3a8a;
  border: none;
  padding: var(--space-8) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.achievements-toggle:hover {
  background: #ffed4e;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.achievement-count {
  background: #1e3a8a;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
}

.achievements-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--space-16);
  min-width: 280px;
  max-height: 400px;
  overflow-y: auto;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--duration-normal) var(--ease-standard);
  z-index: 1000;
  border: 1px solid var(--color-border);
}

.achievements-dropdown.open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.achievements-dropdown h3 {
  margin: 0 0 var(--space-12) 0;
  color: #1e3a8a;
  font-size: var(--font-size-lg);
}

.achievements-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  padding: var(--space-8);
  border-radius: var(--radius-sm);
  transition: background var(--duration-fast) var(--ease-standard);
}

.achievement-item.earned {
  background: rgba(255, 215, 0, 0.1);
}

.achievement-item.locked {
  opacity: 0.5;
}

.achievement-icon {
  font-size: var(--font-size-xl);
  width: 32px;
  text-align: center;
}

.achievement-info {
  flex: 1;
}

.achievement-title {
  font-weight: var(--font-weight-semibold);
  color: #1e3a8a;
  margin: 0;
  font-size: var(--font-size-sm);
}

.achievement-description {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin: 0;
}

/* Notification System */
.notification-container {
  position: fixed;
  top: var(--space-20);
  right: var(--space-20);
  z-index: 2000;
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
  max-width: 320px;
}

.notification {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--space-16);
  box-shadow: var(--shadow-lg);
  border-left: 4px solid #ffd700;
  transform: translateX(100%);
  opacity: 0;
  transition: all var(--duration-normal) var(--ease-standard);
  display: flex;
  align-items: flex-start;
  gap: var(--space-12);
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification.success {
  border-left-color: var(--color-success);
}

.notification.error {
  border-left-color: var(--color-error);
}

.notification.info {
  border-left-color: var(--color-info);
}

.notification-icon {
  font-size: var(--font-size-lg);
  margin-top: var(--space-2);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: var(--font-weight-semibold);
  color: #1e3a8a;
  margin: 0 0 var(--space-4) 0;
  font-size: var(--font-size-sm);
}

.notification-message {
  font-size: var(--font-size-sm);
  color: var(--color-text);
  margin: 0;
}

.notification-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-sm);
  transition: color var(--duration-fast) var(--ease-standard);
}

.notification-close:hover {
  color: var(--color-text);
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(30, 58, 138, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--duration-normal) var(--ease-standard);
}

.loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  text-align: center;
  color: white;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #ffd700;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-16) auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Micro-interactions */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

.interactive-btn:focus {
  animation: pulse 1s infinite;
}

.interactive-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
}

.interactive-btn.success {
  animation: bounce 0.6s ease-in-out;
}

.interactive-btn.error {
  animation: shake 0.5s ease-in-out;
}

/* Enhanced Focus Management */
.focus-trap {
  outline: 2px solid #ffd700;
  outline-offset: 2px;
}

/* Skip Link for Accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #1e3a8a;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 4000;
  transition: top var(--duration-fast) var(--ease-standard);
}

.skip-link:focus {
  top: 6px;
}

/* Touch-friendly improvements for mobile */
@media (max-width: 768px) {
  .interactive-btn {
    min-height: 44px;
    min-width: 44px;
    padding: var(--space-12) var(--space-16);
  }

  .achievements-toggle {
    min-height: 44px;
    padding: var(--space-12) var(--space-16);
  }

  .nav-btn {
    min-height: 44px;
    padding: var(--space-16) var(--space-20);
  }

  .notification-container {
    left: var(--space-12);
    right: var(--space-12);
    max-width: none;
  }

  .achievements-dropdown {
    left: 0;
    right: 0;
    min-width: auto;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .progress-fill {
    background: #000;
  }

  .achievement-count {
    background: #000;
    color: #fff;
  }

  .notification {
    border-width: 2px;
    border-style: solid;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .interactive-btn:hover {
    transform: none;
  }

  .notification {
    transition: opacity var(--duration-fast) ease;
  }

  .spinner {
    animation: none;
  }

  .interactive-btn:focus {
    animation: none;
    outline: 2px solid #ffd700;
  }
}